<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الأنشطة - نظام إدارة الموظفين</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="pagination-styles.css">
    <link rel="stylesheet" href="no-sidebar-layout.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="pagination-system.js" defer></script>
    <style>
        /* تنسيقات خاصة بصفحة سجل الأنشطة */
        .activity-log-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* تنسيق العنوان الجديد */
        .page-title {
            margin-bottom: 30px;
            text-align: right;
        }

        .page-title h1 {
            font-size: 2.2em;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .page-title h1 i {
            color: var(--primary-color);
            font-size: 0.9em;
        }

        /* فلاتر البحث */
        .filters-section {
            background: var(--bg-white);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--border-light);
        }

        .filters-title {
            font-size: 1.3em;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 25px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 10px;
            font-size: 1em;
            font-family: 'Cairo', sans-serif;
        }

        .filter-group input,
        .filter-group select {
            padding: 14px 16px;
            border: 2px solid var(--border-light);
            border-radius: 10px;
            font-size: 1em;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: var(--bg-white);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            transform: translateY(-1px);
        }

        .filter-group input:hover,
        .filter-group select:hover {
            border-color: var(--primary-hover);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .filters-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .filters-actions .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filters-actions .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .filters-actions .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .filters-actions .btn-primary:hover {
            background: var(--primary-hover);
        }

        .filters-actions .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .filters-actions .btn-secondary:hover {
            background: #5a6268;
        }

        .filters-actions .btn-success {
            background: var(--success-color);
            color: white;
        }

        .filters-actions .btn-success:hover {
            background: var(--success-hover);
        }

        .filters-actions .btn-info {
            background: var(--info-color);
            color: white;
        }

        .filters-actions .btn-info:hover {
            background: var(--info-hover);
        }

        /* إحصائيات */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: var(--bg-white);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--border-light);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* جدول السجلات */
        .logs-section {
            background: var(--bg-white);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--border-light);
        }

        .logs-header {
            background: var(--bg-light);
            padding: 20px 25px;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .logs-title {
            font-size: 1.3em;
            font-weight: 600;
            color: var(--text-primary);
        }

        .logs-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .logs-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 1em;
            font-family: 'Cairo', sans-serif;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        .logs-table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 16px 14px;
            text-align: right;
            font-weight: 700;
            color: var(--text-primary);
            border-bottom: 2px solid var(--border-color);
            white-space: nowrap;
            font-family: 'Cairo', sans-serif;
            font-size: 0.95em;
        }

        .logs-table td {
            padding: 14px;
            border-bottom: 1px solid var(--border-light);
            vertical-align: top;
            font-family: 'Cairo', sans-serif;
            line-height: 1.5;
        }

        .logs-table tr:hover {
            background: #f8f9fa;
            transition: background-color 0.2s ease;
        }

        /* تنسيق الصف المميز (آخر نشاط) */
        .logs-table tbody tr:first-child {
            background-color: #e8f5e8 !important;
            border: 2px solid #4CAF50;
        }

        .logs-table tbody tr:first-child td {
            font-weight: bold;
        }

        .logs-table tbody tr:first-child:hover {
            background-color: #d4edda !important;
        }

        .action-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .action-add { background: #e8f5e8; color: #2e7d32; }
        .action-edit { background: #fff3e0; color: #f57c00; }
        .action-delete { background: #ffebee; color: #d32f2f; }
        .action-login { background: #e3f2fd; color: #1976d2; }
        .action-logout { background: #f3e5f5; color: #7b1fa2; }
        .action-print { background: #f1f8e9; color: #388e3c; }
        .action-deliver { background: #e0f2f1; color: #00695c; }
        .action-return { background: #fce4ec; color: #c2185b; }

        .module-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.8em;
            font-weight: 500;
            background: var(--bg-gray);
            color: var(--text-primary);
        }

        .message-cell {
            max-width: 300px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .date-cell {
            white-space: nowrap;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: var(--text-secondary);
        }

        /* Loading و Empty States */
        .loading-state,
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .loading-spinner {
            border: 3px solid var(--border-light);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: var(--bg-light);
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            background: var(--bg-white);
            color: var(--text-primary);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current-page {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .activity-log-container {
                padding: 10px;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .stats-section {
                grid-template-columns: repeat(2, 1fr);
            }

            .logs-table {
                font-size: 0.85em;
            }

            .logs-table th,
            .logs-table td {
                padding: 8px 6px;
            }

            .message-cell {
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- زر العودة للوحة التحكم -->
    <div class="back-to-dashboard">
        <a href="dashboard.html" class="dashboard-btn">
            <i class="fas fa-home"></i>
            <span>العودة للوحة التحكم</span>
        </a>
    </div>

    <div class="main-content full-width">
        <div class="activity-log-container">
            <!-- رأس الصفحة -->
            <div class="page-title">
                <h1><i class="fas fa-clipboard-list"></i> سجل الأنشطة</h1>
            </div>

            <!-- قسم الفلاتر -->
            <div class="filters-section">
                <div class="filters-title">
                    🔍 فلاتر البحث
                </div>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="usernameFilter">اسم المستخدم</label>
                        <input type="text" id="usernameFilter" placeholder="البحث باسم المستخدم...">
                    </div>
                    <div class="filter-group">
                        <label for="actionTypeFilter">نوع العملية</label>
                        <select id="actionTypeFilter">
                            <option value="">جميع العمليات</option>
                            <option value="add">إضافة</option>
                            <option value="edit">تعديل</option>
                            <option value="delete">حذف</option>
                            <option value="login">تسجيل دخول</option>
                            <option value="logout">تسجيل خروج</option>
                            <option value="print">طباعة</option>
                            <option value="deliver">تسليم</option>
                            <option value="return">استرجاع</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="moduleFilter">القسم</label>
                        <select id="moduleFilter">
                            <option value="">جميع الأقسام</option>
                            <option value="employees">الموظفين</option>
                            <option value="vacations">الإجازات</option>
                            <option value="custody">العهد</option>
                            <option value="contributions">المساهمات</option>
                            <option value="rewards">المكافآت</option>
                            <option value="penalties">الخصومات</option>
                            <option value="deductions">الخصومات</option>
                            <option value="evaluations">التقييمات</option>
                            <option value="training">التدريب</option>
                            <option value="resignations">الاستقالات</option>
                            <option value="salary_advances">السلف</option>
                            <option value="extra_hours">الساعات الإضافية</option>
                            <option value="ideal_employees">العمال المثاليين</option>
                            <option value="system">النظام</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="dateFromFilter">من تاريخ</label>
                        <input type="date" id="dateFromFilter">
                    </div>
                    <div class="filter-group">
                        <label for="dateToFilter">إلى تاريخ</label>
                        <input type="date" id="dateToFilter">
                    </div>
                </div>
                <div class="filters-actions">
                    <button type="button" class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-search"></i> تطبيق الفلاتر
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                        <i class="fas fa-trash-alt"></i> مسح الفلاتر
                    </button>
                    <button type="button" class="btn btn-success" onclick="exportLogs()">
                        <i class="fas fa-file-export"></i> تصدير البيانات
                    </button>
                    <button type="button" class="btn btn-info" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- قسم الإحصائيات -->
            <div class="stats-section" id="statsSection">
                <div class="stat-card">
                    <div class="stat-number" id="totalCount">-</div>
                    <div class="stat-label">إجمالي السجلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="uniqueUsers">-</div>
                    <div class="stat-label">المستخدمين النشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="uniqueModules">-</div>
                    <div class="stat-label">الأقسام المستخدمة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="uniqueActions">-</div>
                    <div class="stat-label">أنواع العمليات</div>
                </div>
            </div>

            <!-- قسم السجلات -->
            <div class="logs-section">
                <div class="logs-header">
                    <div class="logs-title"><i class="fas fa-clipboard-list"></i> سجل الأنشطة</div>
                    <div class="logs-actions">
                        <span id="recordsCount" class="text-muted">جاري التحميل...</span>
                        <div class="items-per-page">
                            <label for="activityLogItemsPerPageSelect">عدد العناصر في الصفحة:</label>
                            <select id="activityLogItemsPerPageSelect">
                                <option value="25">25</option>
                                <option value="50" selected>50</option>
                                <option value="100">100</option>
                                <option value="200">200</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="logsTableContainer">
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <p>جاري تحميل السجلات...</p>
                    </div>
                </div>

                <!-- نظام الصفحات لسجل الأنشطة -->
                <div class="pagination-container" id="activityLogPaginationContainer"></div>

                <div class="pagination" id="paginationContainer" style="display: none;">
                    <button id="prevPage" onclick="changePage(-1)">السابق</button>
                    <span id="pageInfo">صفحة 1 من 1</span>
                    <button id="nextPage" onclick="changePage(1)">التالي</button>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="shared-utils.js"></script>
    <script src="universal-pagination-integration.js"></script>
    <script>
        // متغيرات عامة
        let currentPage = 1;
        const recordsPerPage = 50;
        let totalRecords = 0;
        let currentFilters = {};

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadActivityLogs();
        });

        // تحميل سجل الأنشطة
        async function loadActivityLogs() {
            try {
                showLoading();
                
                const params = new URLSearchParams({
                    ...currentFilters,
                    limit: recordsPerPage,
                    offset: (currentPage - 1) * recordsPerPage
                });

                const response = await fetch(`http://localhost:5500/api/activity-logs?${params}`);
                const result = await response.json();

                if (result.success) {
                    displayLogs(result.data);
                    updateStats(result.stats);
                    updatePagination(result.stats.total_count);
                } else {
                    showError('فشل في تحميل السجلات');
                }

            } catch (error) {
                console.error('خطأ في تحميل السجلات:', error);
                showError('حدث خطأ في تحميل السجلات');
            }
        }

        // عرض السجلات
        function displayLogs(logs) {
            const container = document.getElementById('logsTableContainer');
            
            if (logs.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <p>📭 لا توجد سجلات متاحة</p>
                    </div>
                `;
                return;
            }

            const tableHTML = `
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>العملية</th>
                            <th>القسم</th>
                            <th>الوصف</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${logs.map(log => `
                            <tr>
                                <td><strong>${log.username}</strong></td>
                                <td><span class="action-badge action-${log.action_type}">${getActionLabel(log.action_type)}</span></td>
                                <td><span class="module-badge">${getModuleLabel(log.module)}</span></td>
                                <td class="message-cell">${log.message}</td>
                                <td class="date-cell">${log.formatted_date}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // تحديث الإحصائيات
        function updateStats(stats) {
            document.getElementById('totalCount').textContent = stats.total_count.toLocaleString();
            document.getElementById('uniqueUsers').textContent = stats.unique_users;
            document.getElementById('uniqueModules').textContent = stats.unique_modules;
            document.getElementById('uniqueActions').textContent = stats.unique_actions;
            
            document.getElementById('recordsCount').textContent = 
                `عرض ${Math.min(recordsPerPage, stats.total_count)} من أصل ${stats.total_count.toLocaleString()} سجل`;
        }

        // تحديث التصفح
        function updatePagination(totalCount) {
            totalRecords = totalCount;
            const totalPages = Math.ceil(totalCount / recordsPerPage);
            
            document.getElementById('pageInfo').textContent = `صفحة ${currentPage} من ${totalPages}`;
            document.getElementById('prevPage').disabled = currentPage <= 1;
            document.getElementById('nextPage').disabled = currentPage >= totalPages;
            
            document.getElementById('paginationContainer').style.display = totalPages > 1 ? 'flex' : 'none';
        }

        // تغيير الصفحة
        function changePage(direction) {
            const totalPages = Math.ceil(totalRecords / recordsPerPage);
            const newPage = currentPage + direction;
            
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadActivityLogs();
            }
        }

        // تطبيق الفلاتر
        function applyFilters() {
            currentFilters = {
                username: document.getElementById('usernameFilter').value.trim(),
                action_type: document.getElementById('actionTypeFilter').value,
                module: document.getElementById('moduleFilter').value,
                date_from: document.getElementById('dateFromFilter').value,
                date_to: document.getElementById('dateToFilter').value
            };

            // إزالة القيم الفارغة
            Object.keys(currentFilters).forEach(key => {
                if (!currentFilters[key]) {
                    delete currentFilters[key];
                }
            });

            currentPage = 1;
            loadActivityLogs();
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('usernameFilter').value = '';
            document.getElementById('actionTypeFilter').value = '';
            document.getElementById('moduleFilter').value = '';
            document.getElementById('dateFromFilter').value = '';
            document.getElementById('dateToFilter').value = '';
            
            currentFilters = {};
            currentPage = 1;
            loadActivityLogs();
        }

        // تصدير السجلات
        function exportLogs() {
            const params = new URLSearchParams(currentFilters);
            window.open(`http://localhost:5500/api/activity-logs/export?${params}`, '_blank');
        }

        // تحديث السجلات
        function refreshLogs() {
            loadActivityLogs();
        }

        // عرض حالة التحميل
        function showLoading() {
            document.getElementById('logsTableContainer').innerHTML = `
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>جاري تحميل السجلات...</p>
                </div>
            `;
        }

        // عرض رسالة خطأ
        function showError(message) {
            document.getElementById('logsTableContainer').innerHTML = `
                <div class="empty-state">
                    <p>❌ ${message}</p>
                </div>
            `;
        }

        // الحصول على تسمية العملية
        function getActionLabel(action) {
            const labels = {
                'add': 'إضافة',
                'edit': 'تعديل',
                'delete': 'حذف',
                'login': 'دخول',
                'logout': 'خروج',
                'print': 'طباعة',
                'deliver': 'تسليم',
                'return': 'استرجاع'
            };
            return labels[action] || action;
        }

        // الحصول على تسمية القسم
        function getModuleLabel(module) {
            const labels = {
                'employees': 'الموظفين',
                'vacations': 'الإجازات',
                'custody': 'العهد',
                'contributions': 'المساهمات',
                'rewards': 'المكافآت',
                'penalties': 'الخصومات',
                'deductions': 'الخصومات',
                'evaluations': 'التقييمات',
                'training': 'التدريب',
                'resignations': 'الاستقالات',
                'salary_advances': 'السلف',
                'extra_hours': 'الساعات الإضافية',
                'ideal_employees': 'العمال المثاليين',
                'system': 'النظام'
            };
            return labels[module] || module;
        }

        // تطبيق الفلاتر عند الضغط على Enter
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    </script>
</body>
</html>
