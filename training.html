<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قسم التدريب</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <link rel="stylesheet" href="pagination-styles.css" />
  <script src="permissions.js" defer></script>
  <script src="pagination-system.js" defer></script>
  <script src="pagination-styles-fix.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body class="training-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="training-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة التدريب</span>
    </a>
  </div>

  <div class="main-content full-width" id="mainContent">
    <h1>قسم التدريب</h1>


    <!-- تبويب إضافة دورة تدريبية -->
    <div class="tab-content" id="add-training" style="display: none;">
      <form class="training-form">
        <div class="form-group">
          <label for="employeeSearchAdd">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="employeeSearchAdd" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
          <datalist id="employeeSearchSuggestions"></datalist>
          <small class="search-hint">اكتب كود الموظف أو جزء من الاسم ثم اختر من القائمة</small>
        </div>
        <div class="form-group">
          <label for="employeeCode">كود الموظف:</label>
          <input type="text" id="employeeCode" placeholder="كود الموظف" readonly>
        </div>
        <div class="form-group">
          <label for="employeeName">اسم الموظف:</label>
          <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
        </div>
        <div class="form-group">
          <label for="employeeDepartment">الإدارة:</label>
          <input type="text" id="employeeDepartment" placeholder="الإدارة" readonly>
        </div>
        <div class="form-group">
          <label for="courseName">اسم الدورة:</label>
          <input type="text" id="courseName" placeholder="أدخل اسم الدورة التدريبية" required>
        </div>
        <div class="form-group">
          <label for="courseDate">تاريخ الدورة:</label>
          <input type="date" id="courseDate" required>
        </div>
        <div class="form-group">
          <label for="courseDuration">مدة الدورة (بالأيام):</label>
          <input type="number" id="courseDuration" placeholder="عدد أيام الدورة" min="1" required>
        </div>
        <div class="form-group">
          <label for="trainingType">نوع التدريب:</label>
          <select id="trainingType" required>
            <option value="">اختر نوع التدريب</option>
            <option value="تدريب داخلي">تدريب داخلي</option>
            <option value="تدريب خارجي">تدريب خارجي</option>
            <option value="تدريب خاص">تدريب خاص</option>
          </select>
        </div>
        <div class="form-group">
          <label for="courseCost">تكلفة الدورة:</label>
          <input type="number" id="courseCost" placeholder="تكلفة الدورة" min="0" step="0.01">
        </div>
        <div class="form-group full-width">
          <label for="trainingNotes">ملاحظات:</label>
          <textarea id="trainingNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
        </div>
        <div class="form-actions">
          <button id="saveTraining" class="save-btn" type="button">حفظ الدورة التدريبية</button>
          <button id="resetTrainingForm" class="reset-btn" type="button">إعادة تعيين</button>
        </div>
      </form>
      <!-- جدول الدورات التدريبية -->
      <div class="training-table-container">
        <h3>قائمة الدورات التدريبية</h3>

        <!-- فلاتر البحث المحددة -->
        <div class="filtered-search-container">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="filterTrainingEmployeeCode">كود الموظف:</label>
              <input type="text" id="filterTrainingEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterTrainingEmployeeName">اسم الموظف:</label>
              <input type="text" id="filterTrainingEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterTrainingFromDate">من تاريخ:</label>
              <input type="date" id="filterTrainingFromDate" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterTrainingToDate">إلى تاريخ:</label>
              <input type="date" id="filterTrainingToDate" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="clearTrainingFiltersBtn" class="reset-btn">مسح الفلاتر</button>
            <button id="exportTrainingBtn" class="export-btn">تصدير إلى Excel</button>
            <div class="items-per-page">
              <label for="trainingItemsPerPageSelect">عدد العناصر في الصفحة:</label>
              <select id="trainingItemsPerPageSelect">
                <option value="25">25</option>
                <option value="50" selected>50</option>
                <option value="100">100</option>
                <option value="200">200</option>
              </select>
            </div>
          </div>
        </div>
        <table class="training-table" id="training-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>اسم الدورة</th>
              <th>تاريخ الدورة</th>
              <th>مدة الدورة</th>
              <th>نوع التدريب</th>
              <th>التكلفة</th>
              <th>ملاحظات</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
          </tbody>
        </table>

        <!-- نظام الصفحات للتدريب -->
        <div class="pagination-container" id="trainingPaginationContainer"></div>
      </div>
    </div>

    <!-- تبويب تقارير التدريب -->
    <div class="tab-content" id="training-reports" style="display: none;">
      <div class="reports-section">
        <h3>تقارير التدريب</h3>
        
        <div class="report-filters">
          <div class="filter-group">
            <label for="reportDepartment">الإدارة:</label>
            <select id="reportDepartment">
              <option value="">جميع الإدارات</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="reportTrainingType">نوع التدريب:</label>
            <select id="reportTrainingType">
              <option value="">جميع الأنواع</option>
              <option value="تدريب داخلي">تدريب داخلي</option>
              <option value="تدريب خارجي">تدريب خارجي</option>
              <option value="تدريب خاص">تدريب خاص</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="reportStartDate">من تاريخ:</label>
            <input type="date" id="reportStartDate">
          </div>

          <div class="filter-group">
            <label for="reportEndDate">إلى تاريخ:</label>
            <input type="date" id="reportEndDate">
          </div>

          <button id="generateReport" class="generate-btn">إنشاء التقرير</button>
          <button id="exportReportBtn" class="export-btn">تصدير التقرير</button>
          <div class="items-per-page">
            <label for="trainingReportItemsPerPageSelect">عدد العناصر في الصفحة:</label>
            <select id="trainingReportItemsPerPageSelect">
              <option value="25">25</option>
              <option value="50" selected>50</option>
              <option value="100">100</option>
              <option value="200">200</option>
            </select>
          </div>
        </div>

        <div class="report-results">
          <div class="report-summary">
            <div class="summary-card">
              <h4>إجمالي الدورات</h4>
              <span id="totalCourses">0</span>
            </div>
            <div class="summary-card">
              <h4>إجمالي التكلفة</h4>
              <span id="totalCost">0</span>
            </div>
            <div class="summary-card">
              <h4>متوسط مدة الدورة</h4>
              <span id="avgDuration">0</span>
            </div>
          </div>

          <table class="report-table" id="report-table">
            <thead>
              <tr>
                <th>كود الموظف</th>
                <th>اسم الموظف</th>
                <th>الإدارة</th>
                <th>اسم الدورة</th>
                <th>تاريخ الدورة</th>
                <th>مدة الدورة</th>
                <th>نوع التدريب</th>
                <th>التكلفة</th>
              </tr>
            </thead>
            <tbody>
              <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
            </tbody>
          </table>

          <!-- نظام الصفحات لتقارير التدريب -->
          <div class="pagination-container" id="trainingReportPaginationContainer"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- نافذة تعديل الدورة التدريبية -->
  <div class="modal-overlay" id="editTrainingModal" style="display: none;" onclick="closeEditModal()">
    <div class="modal-content" onclick="event.stopPropagation()">
      <div class="modal-header">
        <h3>تعديل الدورة التدريبية</h3>
        <button class="modal-close" onclick="closeEditModal()">&times;</button>
      </div>

      <div class="modal-body">
        <form id="editTrainingForm">
          <!-- البحث عن الموظف -->
          <div class="form-row">
            <div class="form-group full-width">
              <label for="editEmployeeSearch">البحث عن الموظف (بالاسم أو الكود):</label>
              <input type="text" id="editEmployeeSearch" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="editEmployeeSearchSuggestions" autocomplete="off">
              <datalist id="editEmployeeSearchSuggestions"></datalist>
              <small class="search-hint">اكتب كود الموظف أو جزء من الاسم ثم اختر من القائمة</small>
            </div>
          </div>

          <!-- الصف الأول: كود الموظف، اسم الموظف، الإدارة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editEmployeeCode">كود الموظف:</label>
              <input type="text" id="editEmployeeCode" placeholder="كود الموظف" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeName">اسم الموظف:</label>
              <input type="text" id="editEmployeeName" placeholder="اسم الموظف" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeDepartment">الإدارة:</label>
              <input type="text" id="editEmployeeDepartment" placeholder="الإدارة" readonly>
            </div>
          </div>

          <!-- الصف الثاني: اسم الدورة، تاريخ الدورة، مدة الدورة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editCourseName">اسم الدورة:</label>
              <input type="text" id="editCourseName" placeholder="أدخل اسم الدورة التدريبية" required>
            </div>

            <div class="form-group">
              <label for="editCourseDate">تاريخ الدورة:</label>
              <input type="date" id="editCourseDate" required>
            </div>

            <div class="form-group">
              <label for="editCourseDuration">مدة الدورة (بالأيام):</label>
              <input type="number" id="editCourseDuration" placeholder="عدد أيام الدورة" min="1" required>
            </div>
          </div>

          <!-- الصف الثالث: نوع التدريب، تكلفة الدورة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editTrainingType">نوع التدريب:</label>
              <select id="editTrainingType" required>
                <option value="">اختر نوع التدريب</option>
                <option value="تدريب داخلي">تدريب داخلي</option>
                <option value="تدريب خارجي">تدريب خارجي</option>
                <option value="تدريب خاص">تدريب خاص</option>
              </select>
            </div>

            <div class="form-group">
              <label for="editCourseCost">تكلفة الدورة:</label>
              <input type="number" id="editCourseCost" placeholder="تكلفة الدورة" min="0" step="0.01">
            </div>

            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
          </div>

          <!-- الصف الرابع: الملاحظات -->
          <div class="form-row">
            <div class="form-group full-width">
              <label for="editTrainingNotes">ملاحظات:</label>
              <textarea id="editTrainingNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button id="updateTraining" class="save-btn">تحديث الدورة التدريبية</button>
        <button type="button" class="cancel-btn" onclick="closeEditModal()">إلغاء</button>
      </div>
    </div>
  </div>

  <script src="dateUtils.js"></script>
  <script src="form-validation.js"></script>
  <script src="universal-pagination-integration.js"></script>
  <script src="training.js"></script>
  
</body>
</html>
