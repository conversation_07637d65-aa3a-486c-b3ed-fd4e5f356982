<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الساعات الإضافية</title>
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="extraHours.css">
    <link rel="stylesheet" href="pagination-styles.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
    <script src="permissions.js" defer></script>
    <script src="pagination-system.js" defer></script>
</head>

<body class="extra-hours-page">

  <!-- زر العودة لإدارة الساعات الإضافية -->
  <div class="back-to-dashboard">
    <a href="extra-hours-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة الساعات الإضافية</span>
    </a>
  </div>

<!-- المحتوى الرئيسي -->
<main class="main-content full-width" id="mainContent">

    <h1>نظام الساعات الإضافية</h1>



    <div class="tab-content active" id="add-extra">
      <div class="extra-hour-form">
        <form id="extraHourForm" class="modern-form">
          <div class="form-grid">
            <!-- البحث عن الموظف - عرض كامل -->
            <div class="form-group full-width">
              <label for="employeeSearchAdd">البحث عن الموظف (بالاسم أو الكود):</label>
              <input type="text" id="employeeSearchAdd" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
              <datalist id="employeeSearchSuggestions"></datalist>
            </div>

            <!-- الصف الأول - 4 حقول -->
            <div class="form-group">
              <label for="employeeCode">كود الموظف:</label>
              <input type="text" id="employeeCode" placeholder="كود الموظف" readonly>
            </div>

            <div class="form-group">
              <label for="employeeName">اسم الموظف:</label>
              <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
            </div>

            <div class="form-group">
              <label for="employeeDepartment">الإدارة:</label>
              <input type="text" id="employeeDepartment" placeholder="الإدارة" readonly>
            </div>

            <div class="form-group">
              <label for="employeeJob">الوظيفة:</label>
              <input type="text" id="employeeJob" placeholder="الوظيفة" readonly>
            </div>

            <!-- الصف الثاني - 4 حقول -->
            <div class="form-group">
              <label for="extraHours">عدد الساعات الإضافية:</label>
              <input type="number" id="extraHours" placeholder="عدد الساعات" min="0.5" step="0.5" required>
            </div>

            <div class="form-group">
              <label for="extraDate">تاريخ الإضافي:</label>
              <input type="date" id="extraDate" required>
            </div>

            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->

            <!-- الملاحظات - عرض كامل -->
            <div class="form-group full-width">
              <label for="notes">ملاحظات:</label>
              <textarea id="notes" placeholder="ملاحظات إضافية (اختياري)" rows="3"></textarea>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="form-actions full-width">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i>
                حفظ العمل الإضافي
              </button>
              <button type="button" class="btn btn-secondary" onclick="clearForm()">
                <i class="fas fa-eraser"></i>
                مسح النموذج
              </button>
            </div>
          </div>
        </form>
      </div>


      <!-- جدول الساعات الإضافية المضافة -->
      <div class="added-extra-hours-container">
        <h3>الساعات الإضافية المضافة</h3>

        <!-- فلاتر البحث المحددة للساعات الإضافية المضافة -->
        <div class="search-filters-container">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="filterExtraEmployeeCode">كود الموظف:</label>
              <input type="text" id="filterExtraEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterExtraEmployeeName">اسم الموظف:</label>
              <input type="text" id="filterExtraEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterExtraFromDate">من تاريخ:</label>
              <input type="date" id="filterExtraFromDate" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterExtraToDate">إلى تاريخ:</label>
              <input type="date" id="filterExtraToDate" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="applyExtraFiltersBtn" class="search-btn">تطبيق الفلاتر</button>
            <button id="clearExtraFiltersBtn" class="reset-btn">مسح الفلاتر</button>
            <div class="items-per-page">
              <label for="extraHoursItemsPerPageSelect">عدد العناصر في الصفحة:</label>
              <select id="extraHoursItemsPerPageSelect">
                <option value="25">25</option>
                <option value="50" selected>50</option>
                <option value="100">100</option>
                <option value="200">200</option>
              </select>
            </div>
          </div>
        </div>

        <table class="extra-hours-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>عدد الساعات</th>
              <th>تاريخ الإضافي</th>
              <th>ملاحظات</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="extraHoursTableBody">
            <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
          </tbody>
        </table>

        <!-- نظام الصفحات للساعات الإضافية -->
        <div class="pagination-container" id="extraHoursPaginationContainer"></div>
      </div>
    </div>

    <div class="tab-content" id="reports" style="display: none;">
      <div class="reports-section">
        <h3>تقارير الساعات الإضافية</h3>

        <div class="report-filters">
          <div class="filter-group">
            <label for="filterDepartment">الإدارة:</label>
            <select id="filterDepartment">
              <option value="">جميع الإدارات</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="filterEmployee">الموظف:</label>
            <input type="text" id="filterEmployee" placeholder="البحث عن موظف" list="filterEmployeeSuggestions" autocomplete="off">
            <datalist id="filterEmployeeSuggestions"></datalist>
          </div>

          <div class="filter-group">
            <label for="filterStartDate">من تاريخ:</label>
            <input type="date" id="filterStartDate">
          </div>

          <div class="filter-group">
            <label for="filterEndDate">إلى تاريخ:</label>
            <input type="date" id="filterEndDate">
          </div>

          <button id="generateReport" class="generate-btn">إنشاء التقرير</button>
          <button id="exportReportBtn" class="export-btn">تصدير التقرير</button>
        </div>

        <div class="report-results">
          <div class="report-summary">
            <div class="summary-card">
              <h4>إجمالي السجلات</h4>
              <span id="totalRecords">0</span>
            </div>
            <div class="summary-card">
              <h4>إجمالي الساعات</h4>
              <span id="totalHours">0</span>
            </div>
            <div class="summary-card">
              <h4>متوسط الساعات</h4>
              <span id="avgHours">0</span>
            </div>
          </div>

          <table class="report-table" id="reportsTable">
            <thead>
              <tr>
                <th>كود الموظف</th>
                <th>اسم الموظف</th>
                <th>الإدارة</th>
                <th>عدد الساعات</th>
                <th>تاريخ الإضافي</th>
                <th>ملاحظات</th>
              </tr>
            </thead>
            <tbody id="reportsTableBody">
              <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- نافذة تعديل العمل الإضافي -->
  <div class="modal-overlay" id="editExtraHourModal" style="display: none;" onclick="closeEditModal()">
    <div class="modal-content" onclick="event.stopPropagation()">
      <div class="modal-header">
        <h3>تعديل العمل الإضافي</h3>
        <button class="modal-close" onclick="closeEditModal()">&times;</button>
      </div>

      <div class="modal-body">
        <form id="editExtraHourForm">
          <!-- البحث عن الموظف -->
          <div class="form-row">
            <div class="form-group full-width">
              <label for="editEmployeeSearch">البحث عن الموظف (بالاسم أو الكود):</label>
              <input type="text" id="editEmployeeSearch" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="editEmployeeSearchSuggestions" autocomplete="off">
              <datalist id="editEmployeeSearchSuggestions"></datalist>
              <small class="search-hint">اكتب كود الموظف أو جزء من الاسم ثم اختر من القائمة</small>
            </div>
          </div>

          <!-- الصف الأول: كود الموظف، اسم الموظف، الإدارة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editEmployeeCode">كود الموظف:</label>
              <input type="text" id="editEmployeeCode" placeholder="كود الموظف" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeName">اسم الموظف:</label>
              <input type="text" id="editEmployeeName" placeholder="اسم الموظف" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeDepartment">الإدارة:</label>
              <input type="text" id="editEmployeeDepartment" placeholder="الإدارة" readonly>
            </div>
          </div>

          <!-- الصف الثاني: عدد الساعات، تاريخ الإضافي -->
          <div class="form-row">
            <div class="form-group">
              <label for="editExtraHours">عدد الساعات الإضافية:</label>
              <input type="number" id="editExtraHours" placeholder="عدد الساعات" min="0.5" step="0.5" required>
            </div>

            <div class="form-group">
              <label for="editExtraDate">تاريخ الإضافي:</label>
              <input type="date" id="editExtraDate" required>
            </div>

            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
          </div>

          <!-- الصف الثالث: الملاحظات -->
          <div class="form-row">
            <div class="form-group full-width">
              <label for="editNotes">ملاحظات:</label>
              <textarea id="editNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button id="updateExtraHour" class="save-btn" onclick="updateExtraHour()">تحديث العمل الإضافي</button>
        <button type="button" class="cancel-btn" onclick="closeEditModal()">إلغاء</button>
      </div>
    </div>
  </div>

</main>

<!-- سكريبت الصفحة -->
<script src="arabic-date-picker.js"></script>
<script src="config.js"></script>
<script src="DateUtils.js"></script>
<script src="shared-utils.js"></script>
<script src="universal-pagination-integration.js"></script>
<script src="extraHours.js"></script>


</body>
</html>
