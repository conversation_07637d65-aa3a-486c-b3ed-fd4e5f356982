# نظام الصفحات (Pagination System)

## نظرة عامة
تم تطوير نظام صفحات متقدم لجدول الموظفين يتيح عرض البيانات بشكل منظم ومقسم إلى صفحات منفصلة بدلاً من عرض جميع البيانات في صفحة واحدة.

## المميزات الجديدة

### 1. تقسيم البيانات إلى صفحات
- **الحد الافتراضي**: 50 سطر لكل صفحة
- **خيارات متعددة**: 25، 50، 100، 200 سطر لكل صفحة
- **تحديث فوري**: تغيير عدد العناصر يحدث فورياً

### 2. أزرار التنقل
- **الصفحة السابقة**: للانتقال للصفحة السابقة
- **الصفحة التالية**: للانتقال للصفحة التالية
- **أرقام الصفحات**: أزرار مرقمة للانتقال المباشر
- **الصفحة الأولى والأخيرة**: روابط سريعة عند وجود صفحات كثيرة

### 3. عرض معلومات الصفحة
- عرض رقم الصفحة الحالية
- عرض العناصر المعروضة (مثال: عرض 1-50 من 150 موظف)
- عرض العدد الإجمالي للموظفين

### 4. تصميم متجاوب
- **الحاسوب**: عرض كامل لجميع أزرار التنقل
- **الهواتف المحمولة**: تصميم مبسط ومتجاوب
- **الأجهزة اللوحية**: تصميم متوسط يناسب الشاشة

## كيفية الاستخدام

### تغيير عدد العناصر في الصفحة
1. ابحث عن القائمة المنسدلة "عدد العناصر في الصفحة" أعلى الجدول
2. اختر العدد المطلوب (25، 50، 100، أو 200)
3. سيتم تحديث الجدول فورياً

### التنقل بين الصفحات
1. **استخدام الأزرار**:
   - "السابق": للانتقال للصفحة السابقة
   - "التالي": للانتقال للصفحة التالية
   - **أرقام الصفحات**: انقر على رقم الصفحة للانتقال إليها مباشرة

2. **الصفحات الكثيرة**:
   - عند وجود أكثر من 5 صفحات، سيظهر "..." للإشارة لوجود صفحات إضافية
   - أزرار الصفحة الأولى والأخيرة متاحة دائماً

### البحث والفلترة
- **البحث**: يعمل البحث على جميع البيانات ويعرض النتائج مقسمة إلى صفحات
- **الفلترة**: فلترة الإدارات والوظائف تعمل مع نظام الصفحات
- **إعادة التعيين**: يعيد عرض جميع البيانات مع العودة للصفحة الأولى

## التحسينات التقنية

### 1. الأداء
- تحميل البيانات مرة واحدة فقط
- عرض جزء صغير من البيانات في كل مرة
- تحسين سرعة التصفح والبحث

### 2. تجربة المستخدم
- تصميم بديهي وسهل الاستخدام
- ألوان متناسقة مع تصميم النظام
- رسائل واضحة عند عدم وجود بيانات

### 3. التوافق
- يعمل مع جميع المتصفحات الحديثة
- متوافق مع الهواتف المحمولة والأجهزة اللوحية
- يدعم اللغة العربية بالكامل

## الملفات المحدثة

### 1. index.html
- إضافة عنصر التحكم في عدد العناصر
- إضافة حاوي نظام الصفحات

### 2. script.js
- دوال جديدة لإدارة الصفحات
- تحديث دوال البحث والعرض
- دعم تغيير عدد العناصر

### 3. index-table.css
- تصميم أزرار التنقل
- تصميم متجاوب للهواتف
- تنسيق عنصر التحكم في العدد

## المتغيرات الجديدة

```javascript
let currentPage = 1;           // الصفحة الحالية
let itemsPerPage = 50;         // عدد العناصر في الصفحة
let totalPages = 1;            // العدد الإجمالي للصفحات
let currentDisplayedEmployees = []; // البيانات المعروضة حالياً
```

## الدوال الجديدة

- `calculateTotalPages()`: حساب عدد الصفحات
- `getCurrentPageData()`: الحصول على بيانات الصفحة الحالية
- `createPaginationControls()`: إنشاء أزرار التنقل
- `goToPage()`: الانتقال لصفحة معينة
- `displayEmployeesInTable()`: عرض الموظفين في الجدول

## ملاحظات مهمة

1. **الحفاظ على البيانات**: جميع البيانات محفوظة في الذاكرة، التقسيم للعرض فقط
2. **البحث الشامل**: البحث يعمل على جميع البيانات وليس الصفحة الحالية فقط
3. **التصدير**: تصدير Excel يشمل البيانات المفلترة أو جميع البيانات
4. **الأداء**: تحسين كبير في الأداء عند وجود عدد كبير من الموظفين

## الدعم والصيانة

لأي استفسارات أو مشاكل تقنية، يرجى مراجعة:
- ملف `script.js` للمنطق البرمجي
- ملف `index-table.css` للتصميم
- ملف `index.html` للهيكل الأساسي
