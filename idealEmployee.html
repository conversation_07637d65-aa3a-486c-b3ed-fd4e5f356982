<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>العامل المثالي</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="shared-styles.css">
  <link rel="stylesheet" href="idealEmployee.css">
  <link rel="stylesheet" href="pagination-styles.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <script src="permissions.js" defer></script>
  <script src="pagination-system.js" defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body class="ideal-employee-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="ideal-employee-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة العامل المثالي</span>
    </a>
  </div>


  <div class="main-content full-width" id="mainContent">
    <h1>العامل المثالي</h1>



    <!-- محتوى التبويبات -->
    <div class="tab-content" id="add-ideal-employee">
      <div class="ideal-employee-form">
        <h2>إضافة عامل مثالي جديد</h2>
        
        <form id="idealEmployeeForm">
          <div class="form-row">
            <div class="form-group">
              <label for="employeeSearchAdd">البحث عن الموظف (بالاسم أو الكود):</label>
              <input type="text" id="employeeSearchAdd" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
              <datalist id="employeeSearchSuggestions"></datalist>
            </div>
            <div class="form-group">
              <label for="employeeCode">كود الموظف:</label>
              <input type="text" id="employeeCode" placeholder="كود الموظف" readonly>
            </div>
            <div class="form-group">
              <label for="employeeName">الاسم:</label>
              <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
            </div>
            <div class="form-group">
              <label for="employeeDepartment">الإدارة:</label>
              <input type="text" id="employeeDepartment" placeholder="الإدارة" readonly>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="fromPeriod">من فترة:</label>
              <input type="date" id="fromPeriod" required>
            </div>
            <div class="form-group">
              <label for="toPeriod">إلى فترة:</label>
              <input type="date" id="toPeriod" required>
            </div>
            <div class="form-group">
              <label for="evaluationScore">درجة التقييم:</label>
              <input type="number" id="evaluationScore" min="0" max="100" step="0.1" placeholder="درجة التقييم" required>
            </div>
            <div class="form-group">
              <label for="rewardAmount">مبلغ المكافأة:</label>
              <input type="number" id="rewardAmount" min="0" step="0.01" placeholder="مبلغ المكافأة" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="selectionReason">سبب اختيار الموظف:</label>
              <input type="text" id="selectionReason" placeholder="سبب اختيار الموظف" required>
            </div>
            <div class="form-group">
              <label for="notes">ملاحظات:</label>
              <textarea id="notes" placeholder="ملاحظات إضافية" rows="3"></textarea>
            </div>
            <div class="form-group"></div>
            <div class="form-group"></div>
          </div>

          <div class="form-actions">
            <button type="submit" id="saveIdealEmployee" class="save-btn">
              <i class="fas fa-save"></i>
              حفظ العامل المثالي
            </button>
            <button type="button" id="resetForm" class="reset-btn">
              <i class="fas fa-undo"></i>
              إعادة تعيين
            </button>
          </div>
        </form>
      </div>

      <!-- جدول العمال المثاليين -->
      <div class="ideal-employees-table-container">
        <h3>قائمة العمال المثاليين</h3>

        <!-- فلاتر البحث المحددة -->
        <div class="filtered-search-container">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="filterEmployeeCode">كود الموظف:</label>
              <input type="text" id="filterEmployeeCode" placeholder="أدخل كود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterEmployeeName">اسم الموظف:</label>
              <input type="text" id="filterEmployeeName" placeholder="أدخل اسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterFromDate">من تاريخ:</label>
              <input type="date" id="filterFromDate" class="filter-input">
            </div>

            <div class="form-group">
              <label for="filterToDate">إلى تاريخ:</label>
              <input type="date" id="filterToDate" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="clearFiltersBtn" class="reset-btn">مسح الفلاتر</button>
            <button id="exportIdealEmployees" class="export-btn">
              <i class="fas fa-file-excel"></i>
              تصدير إلى Excel
            </button>
          </div>
        </div>

        <div class="table-controls">
          <button id="reloadIdealEmployees" class="reload-btn">
            <i class="fas fa-sync-alt"></i>
            إعادة تحميل البيانات
          </button>
          <div class="items-per-page">
            <label for="idealEmployeeItemsPerPageSelect">عدد العناصر في الصفحة:</label>
            <select id="idealEmployeeItemsPerPageSelect">
              <option value="25">25</option>
              <option value="50" selected>50</option>
              <option value="100">100</option>
              <option value="200">200</option>
            </select>
          </div>
        </div>
        <table class="ideal-employees-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>الاسم</th>
              <th>الإدارة</th>
              <th>من فترة</th>
              <th>إلى فترة</th>
              <th>درجة التقييم</th>
              <th>مبلغ المكافأة</th>
              <th>سبب الاختيار</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="idealEmployeesTableBody">
            <!-- سيتم ملء البيانات ديناميكياً -->
          </tbody>
        </table>

        <!-- نظام الصفحات للموظف المثالي -->
        <div class="pagination-container" id="idealEmployeePaginationContainer"></div>
      </div>

    <!-- تبويب التقارير -->
    <div class="tab-content" id="reports">
      <div class="reports-container">
        <h2>تقارير العامل المثالي</h2>

        <!-- فلاتر التقارير -->
        <div class="report-filters">
          <div class="filter-row">
            <div class="filter-group">
              <label for="reportStartDate">من تاريخ:</label>
              <input type="date" id="reportStartDate">
            </div>
            <div class="filter-group">
              <label for="reportEndDate">إلى تاريخ:</label>
              <input type="date" id="reportEndDate">
            </div>
            <div class="filter-group">
              <label for="reportDepartment">الإدارة:</label>
              <select id="reportDepartment">
                <option value="">جميع الإدارات</option>
              </select>
            </div>
            <div class="filter-group">
              <label for="reportEmployee">الموظف:</label>
              <input type="text" id="reportEmployee" placeholder="البحث عن موظف" list="reportEmployeeSuggestions" autocomplete="off">
              <datalist id="reportEmployeeSuggestions"></datalist>
            </div>
          </div>
          <div class="filter-actions">
            <button id="generateReport" class="generate-btn">
              <i class="fas fa-search"></i>
              إنشاء التقرير
            </button>
            <button id="resetFilters" class="reset-btn">
              <i class="fas fa-undo"></i>
              إعادة تعيين
            </button>
            <button id="printReport" class="print-btn">
              <i class="fas fa-print"></i>
              طباعة
            </button>
            <button id="exportReport" class="export-btn">
              <i class="fas fa-file-excel"></i>
              تصدير Excel
            </button>
          </div>
        </div>

        <!-- جدول التقارير -->
        <div class="report-table-container">
          <table class="report-table">
            <thead>
              <tr>
                <th>كود الموظف</th>
                <th>الاسم</th>
                <th>الإدارة</th>
                <th>من فترة</th>
                <th>إلى فترة</th>
                <th>درجة التقييم</th>
                <th>مبلغ المكافأة</th>
                <th>سبب الاختيار</th>
                <th>ملاحظات</th>
                <th>تاريخ الإضافة</th>
              </tr>
            </thead>
            <tbody id="reportTableBody">
              <!-- سيتم ملء البيانات ديناميكياً -->
            </tbody>
          </table>
        </div>

        <!-- إحصائيات التقرير -->
        <div class="report-statistics">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-info">
              <h4>إجمالي العمال المثاليين</h4>
              <span id="totalIdealEmployees">0</span>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-info">
              <h4>إجمالي المكافآت</h4>
              <span id="totalRewards">0</span>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-star"></i>
            </div>
            <div class="stat-info">
              <h4>متوسط التقييم</h4>
              <span id="averageScore">0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- نافذة تعديل العامل المثالي -->
  <div class="modal-overlay" id="editIdealEmployeeModal" style="display: none;" onclick="closeEditModal()">
    <div class="modal-content" onclick="event.stopPropagation()">
      <div class="modal-header">
        <h3>تعديل العامل المثالي</h3>
        <button class="modal-close" onclick="closeEditModal()">&times;</button>
      </div>

      <div class="modal-body">
        <form id="editIdealEmployeeForm">
          <input type="hidden" id="editId">

          <div class="form-row">
            <div class="form-group">
              <label for="editEmployeeCode">كود الموظف:</label>
              <input type="text" id="editEmployeeCode" readonly>
            </div>
            <div class="form-group">
              <label for="editEmployeeName">الاسم:</label>
              <input type="text" id="editEmployeeName" readonly>
            </div>
            <div class="form-group">
              <label for="editDepartment">القسم:</label>
              <input type="text" id="editDepartment" readonly>
            </div>
            <div class="form-group">
              <label for="editFromPeriod">من فترة:</label>
              <input type="date" id="editFromPeriod" required>
            </div>
            <div class="form-group">
              <label for="editToPeriod">إلى فترة:</label>
              <input type="date" id="editToPeriod" required>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="editEvaluationScore">درجة التقييم:</label>
              <input type="number" id="editEvaluationScore" min="0" max="100" step="0.1" required>
            </div>
            <div class="form-group">
              <label for="editRewardAmount">مبلغ المكافأة:</label>
              <input type="number" id="editRewardAmount" min="0" step="0.01" required>
            </div>
            <div class="form-group">
              <label for="editSelectionReason">سبب اختيار الموظف:</label>
              <input type="text" id="editSelectionReason" required>
            </div>
            <div class="form-group">
              <label for="editNotes">ملاحظات:</label>
              <textarea id="editNotes" rows="3"></textarea>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button id="updateIdealEmployee" class="save-btn">تحديث العامل المثالي</button>
        <button type="button" class="cancel-btn" onclick="closeEditModal()">إلغاء</button>
      </div>
    </div>
  </div>

  <script src="shared-utils.js"></script>
  <script src="dateUtils.js"></script>
  <script src="arabic-date-picker.js"></script>
  <script src="permissions.js"></script>
  <script src="universal-pagination-integration.js"></script>
  <script src="idealEmployee.js"></script>
  
</body>
</html>
