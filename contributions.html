<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام المساهمات</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="contributions.css">
  <link rel="stylesheet" href="pagination-styles.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <script src="permissions.js" defer></script>
  <script src="pagination-system.js" defer></script>
</head>
<body class="contributions-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="contributions-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة المساهمات</span>
    </a>
  </div>





  <div class="main-content full-width" id="mainContent">
    <h1>نظام المساهمات</h1>



    <div class="tab-content" id="add-contribution" style="display: none;">
      <div class="contribution-form">
        <div class="form-group">
          <label for="employeeSearch">البحث عن الموظف (بالاسم أو الكود):</label>
          <input type="text" id="employeeSearch" class="employee-search-input" placeholder="أدخل اسم أو كود الموظف" list="employeeSearchSuggestions" autocomplete="off">
          <datalist id="employeeSearchSuggestions"></datalist>
        </div>

        <div class="form-group">
          <label for="employeeCode">كود الموظف:</label>
          <input type="text" id="employeeCode" placeholder="أدخل كود الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="employeeName">اسم الموظف:</label>
          <input type="text" id="employeeName" placeholder="اسم الموظف" readonly>
        </div>

        <div class="form-group">
          <label for="contributionType">نوع المساهمة:</label>
          <select id="contributionType" required>
            <option value="">اختر نوع المساهمة</option>
            <option value="1">الزواج</option>
            <option value="2">إنجاب الطفل</option>
            <option value="3">الولادة الطبيعية للزوجة</option>
            <option value="4">الولادة القيصرية للزوجة</option>
            <option value="5">إصابة العمل</option>
            <option value="6">العمليات الجراحية للعامل الغير مؤمن عليه</option>
            <option value="7">العمليات الجراحية للزوجة والأبناء</option>
            <option value="8">المرضى دون الأمراض المزمنة</option>
            <option value="9">الوفاة المؤمن عليه اجتماعيا</option>
            <option value="10">وفاة الموظف في حالة أنه غير مؤمن عليه اجتماعيا</option>
            <option value="11">وفاة أحد الأقارب من الدرجة الأولى (أحد الوالدين - أو الزوجة - أحد الأبناء)</option>
            <option value="12">زواج أحد أبناء العاملين</option>
          </select>
        </div>

        <div class="form-group">
          <label for="companyAmount">مبلغ مساهمة الشركة:</label>
          <input type="number" id="companyAmount" min="0" step="0.01" required>
        </div>

        <div class="form-group">
          <label for="fundAmount">مبلغ مساهمة صندوق الزمالة:</label>
          <input type="number" id="fundAmount" min="0" step="0.01" required>
        </div>



          <div class="form-group">
            <label for="contributionDate">تاريخ المساهمة:</label>
            <input type="date" id="contributionDate" required>
          </div>

        <div class="form-group">
          <label for="notes">ملاحظات:</label>
          <textarea id="notes" rows="3"></textarea>
        </div>

        <div class="form-actions">
          <button id="saveContribution" class="save-btn">حفظ المساهمة</button>
          <button id="resetForm" class="reset-btn">إعادة تعيين</button>
        </div>
      </div>

      <div class="contribution-table-container">
        <h3>قائمة المساهمات</h3>

        <div class="table-controls">
          <div class="items-per-page">
            <label for="contributionsItemsPerPageSelect">عدد العناصر في الصفحة:</label>
            <select id="contributionsItemsPerPageSelect">
              <option value="25">25</option>
              <option value="50" selected>50</option>
              <option value="100">100</option>
              <option value="200">200</option>
            </select>
          </div>
        </div>

        <table class="contribution-table" id="contribution-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>نوع المساهمة</th>
              <th>مبلغ مساهمة الشركة</th>
              <th>مبلغ مساهمة صندوق الزمالة</th>

              <th>التاريخ</th>
              <th>ملاحظات</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="contributionTableBody"></tbody>
        </table>

        <!-- نظام الصفحات للمساهمات -->
        <div class="pagination-container" id="contributionsPaginationContainer"></div>
      </div>
    </div>

    <!-- قسم عرض المساهمات -->
    <div class="tab-content" id="view-contributions" style="display: none;">
      <h2>عرض المساهمات</h2>
      
      <div class="search-filters">
        <div class="form-group">
          <label for="searchEmployeeName">بحث عن موظف:</label>
          <input type="text" id="searchEmployeeName" placeholder="اسم الموظف أو الكود" list="employeeSearchViewSuggestions">
          <datalist id="employeeSearchViewSuggestions"></datalist>
        </div>
        
        <div class="form-group">
          <label for="searchContributionType">نوع المساهمة:</label>
          <select id="searchContributionType">
            <option value="">الكل</option>
            <option value="1">الزواج</option>
            <option value="2">إنجاب الطفل</option>
            <option value="3">الولادة الطبيعية للزوجة</option>
            <option value="4">الولادة القيصرية للزوجة</option>
            <option value="5">إصابة العمل</option>
            <option value="6">العمليات الجراحية للعامل الغير مؤمن عليه</option>
            <option value="7">العمليات الجراحية للزوجة والأبناء</option>
            <option value="8">المرضى دون الأمراض المزمنة</option>
            <option value="9">الوفاة المؤمن عليه اجتماعيا</option>
            <option value="10">وفاة الموظف في حالة أنه غير مؤمن عليه اجتماعيا</option>
            <option value="11">وفاة أحد الأقارب من الدرجة الأولى</option>
            <option value="12">زواج أحد أبناء العاملين</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="searchStartDate">من تاريخ:</label>
          <input type="date" id="searchStartDate">
        </div>
        
        <div class="form-group">
          <label for="searchEndDate">إلى تاريخ:</label>
          <input type="date" id="searchEndDate">
        </div>
        
        <div class="filter-actions">
          <button id="searchContributionsBtn" class="search-btn">بحث</button>
          <button id="resetContributionsBtn" class="reset-btn">إعادة تعيين</button>
        </div>
      </div>
      

      
      <div class="contribution-table-container">
        <div id="filteredContributionsLoading" class="loading-indicator" style="display: none;">
          <div class="spinner"></div>
          <p>جاري تحميل البيانات...</p>
        </div>
        
        <table class="contribution-table" id="filteredContributionsTable">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>نوع المساهمة</th>
              <th>مساهمة الشركة</th>
              <th>مساهمة صندوق الزمالة</th>

              <th>التاريخ</th>
              <th>ملاحظات</th>
            </tr>
          </thead>
          <tbody id="filteredContributionsTableBody"></tbody>
        </table>
        
        <div id="noFilteredContributionsMessage" style="display: none;">
          <p>لا توجد مساهمات تطابق معايير البحث</p>
        </div>
      </div>
    </div>
    
    <!-- قسم تقارير المساهمات -->
    <div class="tab-content" id="contribution-reports" style="display: none;">
      <h2>تقارير المساهمات</h2>
      
      <div class="report-section">
        <h3>🔍 تحديد الفترة الزمنية</h3>
        <div class="search-filters">
          <div class="form-group">
            <label for="reportStartDate">من تاريخ:</label>
            <input type="date" id="reportStartDate">
          </div>
          
          <div class="form-group">
            <label for="reportEndDate">إلى تاريخ:</label>
            <input type="date" id="reportEndDate">
          </div>
          
          <div class="filter-actions">
            <button id="generateReportBtn" class="search-btn">عرض التقرير</button>
          </div>
        </div>
      </div>
      
      <div id="reportResults" style="display: none;">
        <h3 id="reportPeriodTitle" class="report-period-title">تقرير المساهمات</h3>
        
        <div class="report-section">
          <h3>🧮 إجمالي المساهمات</h3>
          
          <div class="summary-cards-container">
            <!-- بطاقة إجمالي المساهمات (الشركة + صندوق الزمالة) -->
            <div class="summary-card total-card">
              <div class="card-icon">💰</div>
              <div class="summary-title">إجمالي المساهمات (الشركة + صندوق الزمالة)</div>
              <div class="summary-value" id="totalContributionsAmount">0</div>
              <div class="summary-unit">جنيه</div>
              <button id="viewTotalDetailsBtn" class="details-btn">عرض التفاصيل</button>
            </div>
            
            <!-- بطاقات مساهمات الشركة -->
            <div class="summary-card company-card">
              <div class="card-icon">💼</div>
              <div class="summary-title">إجمالي مساهمات الشركة</div>
              <div class="summary-value" id="totalCompanyAmount">0</div>
              <div class="summary-unit">جنيه</div>
              <button id="viewCompanyDetailsBtn" class="details-btn">عرض التفاصيل</button>
            </div>
            
            <!-- بطاقات مساهمات صندوق الزمالة -->
            <div class="summary-card fund-card">
              <div class="card-icon">🏦</div>
              <div class="summary-title">إجمالي مساهمات صندوق الزمالة</div>
              <div class="summary-value" id="totalFundAmount">0</div>
              <div class="summary-unit">جنيه</div>
              <button id="viewFundDetailsBtn" class="details-btn">عرض التفاصيل</button>
            </div>
            
            <div class="summary-card company-card">
              <div class="card-icon">👥</div>
              <div class="summary-title">عدد الموظفين المستفيدين من مساهمات الشركة</div>
              <div class="summary-value" id="companyBeneficiaryEmployees">0</div>
              <div class="summary-unit">موظف</div>
            </div>
            
            <div class="summary-card fund-card">
              <div class="card-icon">👥</div>
              <div class="summary-title">عدد الموظفين المستفيدين من مساهمات صندوق الزمالة</div>
              <div class="summary-value" id="fundBeneficiaryEmployees">0</div>
              <div class="summary-unit">موظف</div>
            </div>
            
            <!-- بطاقة إجمالي المستفيدين -->
            <div class="summary-card total-card">
              <div class="card-icon">🌟</div>
              <div class="summary-title">إجمالي عدد الموظفين المستفيدين</div>
              <div class="summary-value" id="totalBeneficiaryEmployees">0</div>
              <div class="summary-unit">موظف</div>
            </div>
          </div>
        </div>
        
        <div class="report-section" id="detailsSection" style="display: none;">
          <h3>📄 تفاصيل المساهمة</h3>
          <h4 id="detailsTitle"></h4>
          
          <div class="contribution-table-container">
            <div class="table-controls">
              <button id="exportDetailsBtn" class="export-btn">تصدير التفاصيل إلى Excel</button>
            </div>
            <table class="contribution-table" id="detailsTable">
              <thead>
                <tr>
                  <th>اسم الموظف</th>
                  <th>قيمة المساهمة</th>
                  <th>نوع المساهمة</th>
                  <th>التاريخ</th>
                  <th>ملاحظات</th>
                </tr>
              </thead>
              <tbody id="detailsTableBody"></tbody>
            </table>
            
            <div id="noDetailsMessage" style="display: none;">
              <p>لا توجد بيانات للعرض</p>
            </div>
          </div>
        </div>
      </div>
      
      <div id="reportLoading" class="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <p>جاري تحميل البيانات...</p>
      </div>
      
      <div id="noReportDataMessage" style="display: none;">
        <p>لا توجد بيانات مساهمات في الفترة الزمنية المحددة</p>
      </div>
    </div>
  </div>

  <!-- Modal for editing contribution -->
  <div id="editContributionModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>تعديل المساهمة</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <form id="editContributionForm">
          <input type="hidden" id="editContributionId">

          <!-- الصف الأول: كود الموظف، اسم الموظف، نوع المساهمة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editEmployeeCode">كود الموظف:</label>
              <input type="text" id="editEmployeeCode" readonly>
            </div>

            <div class="form-group">
              <label for="editEmployeeName">اسم الموظف:</label>
              <input type="text" id="editEmployeeName" readonly>
            </div>

            <div class="form-group">
              <label for="editContributionType">نوع المساهمة:</label>
              <select id="editContributionType" required>
                <option value="">اختر نوع المساهمة</option>
                <option value="1">الزواج</option>
                <option value="2">إنجاب الطفل</option>
                <option value="3">الولادة الطبيعية للزوجة</option>
                <option value="4">الولادة القيصرية للزوجة</option>
                <option value="5">إصابة العمل</option>
                <option value="6">العمليات الجراحية للعامل الغير مؤمن عليه</option>
                <option value="7">العمليات الجراحية للزوجة والأبناء</option>
                <option value="8">المرضى دون الأمراض المزمنة</option>
                <option value="9">الوفاة المؤمن عليه اجتماعيا</option>
                <option value="10">وفاة الموظف في حالة أنه غير مؤمن عليه اجتماعيا</option>
                <option value="11">وفاة أحد الأقارب من الدرجة الأولى</option>
                <option value="12">زواج أحد أبناء العاملين</option>
              </select>
            </div>
          </div>

          <!-- الصف الثاني: مبلغ مساهمة الشركة، مبلغ مساهمة صندوق الزمالة، تاريخ المساهمة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editCompanyAmount">مبلغ مساهمة الشركة:</label>
              <input type="number" id="editCompanyAmount" min="0" step="0.01" required>
            </div>

            <div class="form-group">
              <label for="editFundAmount">مبلغ مساهمة صندوق الزمالة:</label>
              <input type="number" id="editFundAmount" min="0" step="0.01" required>
            </div>

            <div class="form-group">
              <label for="editContributionDate">تاريخ المساهمة:</label>
              <input type="date" id="editContributionDate" required>
            </div>
          </div>

          <!-- الصف الثالث: الملاحظات -->
          <div class="form-row">
            <div class="form-group full-width">
              <label for="editNotes">ملاحظات:</label>
              <textarea id="editNotes" rows="3"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button id="updateContribution" class="save-btn">تحديث</button>
        <button class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>

  <!-- إضافة مكتبة SheetJS لتصدير Excel -->
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <script src="config.js"></script>
  <script src="dateUtils.js"></script>
  <script src="arabic-date-picker.js"></script>
  <script src="form-validation.js"></script>
  <script src="shared-utils.js"></script>
  <script src="contributions.js"></script>

  

<script>
// دالة تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح البيانات المحفوظة
        localStorage.removeItem('token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('activeSection');
        localStorage.removeItem('userName');
        
        // إعادة التوجيه لصفحة تسجيل الدخول
        window.location.href = 'login.html';
    }
}

// تحديث معلومات المستخدم في القائمة الجانبية
// تحميل القائمة الجانبية مع النظام المحسن
</script>
</body>
</html>