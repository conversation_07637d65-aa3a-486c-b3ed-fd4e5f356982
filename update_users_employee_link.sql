-- ==================== تحديث جدول المستخدمين لربطه بالموظفين ====================
-- هذا الملف يحتوي على التحديثات المطلوبة لربط المستخدمين بالموظفين

-- التحقق من وجود قاعدة البيانات واستخدامها
USE employee_system;

-- ==================== إضافة الحقول الجديدة ====================

-- إضافة حقل employee_code لجدول المستخدمين (إذا لم يكن موجوداً)
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'users' 
    AND COLUMN_NAME = 'employee_code'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `users` ADD COLUMN `employee_code` int DEFAULT NULL COMMENT \'كود الموظف المرتبط بالمستخدم\' AFTER `password`', 
    'SELECT "Column employee_code already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- التحقق من وجود حقل permissions وإضافته إذا لم يكن موجوداً
SET @permissions_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'users' 
    AND COLUMN_NAME = 'permissions'
);

SET @sql = IF(@permissions_exists = 0, 
    'ALTER TABLE `users` ADD COLUMN `permissions` JSON DEFAULT NULL COMMENT \'صلاحيات المستخدم\' AFTER `employee_code`', 
    'SELECT "Column permissions already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==================== إضافة الفهارس والمفاتيح الخارجية ====================

-- إضافة فهرس فريد على employee_code (إذا لم يكن موجوداً)
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'users' 
    AND INDEX_NAME = 'uk_employee_code'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE `users` ADD UNIQUE KEY `uk_employee_code` (`employee_code`)', 
    'SELECT "Unique key uk_employee_code already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة فهرس عادي على employee_code (إذا لم يكن موجوداً)
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'users' 
    AND INDEX_NAME = 'idx_employee_code'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE `users` ADD KEY `idx_employee_code` (`employee_code`)', 
    'SELECT "Index idx_employee_code already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة المفتاح الخارجي (مع التحقق من عدم وجوده مسبقاً)
SET @constraint_exists = (
    SELECT COUNT(*) 
    FROM information_schema.TABLE_CONSTRAINTS 
    WHERE CONSTRAINT_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'users' 
    AND CONSTRAINT_NAME = 'fk_users_employee'
);

SET @sql = IF(@constraint_exists = 0, 
    'ALTER TABLE `users` ADD CONSTRAINT `fk_users_employee` FOREIGN KEY (`employee_code`) REFERENCES `employees` (`code`) ON DELETE SET NULL ON UPDATE CASCADE', 
    'SELECT "Foreign key constraint fk_users_employee already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==================== تحديث بيانات المستخدم admin ====================

-- تحديث المستخدم admin ليملك جميع الصلاحيات (إذا كان موجوداً)
UPDATE `users` SET `permissions` = JSON_OBJECT(
  'can_view', true,
  'can_add', true,
  'can_edit', true,
  'can_delete', true,
  'view_employees', true,
  'view_vacations', true,
  'view_contributions', true,
  'view_rewards_penalties', true,
  'view_rewards_list', true,
  'add_reward', true,
  'export_rewards', true,
  'view_deductions_list', true,
  'add_deduction', true,
  'export_deductions', true,
  'view_custody', true,
  'view_evaluation', true,
  'view_training', true,
  'add_training', true,
  'edit_training', true,
  'delete_training', true,
  'view_training_reports', true,
  'view_resignations', true,
  'add_resignation', true,
  'edit_resignation', true,
  'delete_resignation', true,
  'view_resignation_reports', true,
  'view_salary_advances', true,
  'add_salary_advance', true,
  'edit_salary_advance', true,
  'delete_salary_advance', true,
  'view_salary_advance_reports', true,
  'view_extra_hours', true,
  'add_extra_hour', true,
  'edit_extra_hour', true,
  'delete_extra_hour', true,
  'view_extra_hour_reports', true,
  'view_import', true,
  'manage_users', true,
  -- صلاحيات العهد المحددة
  'edit_deliver_custody', true,
  'delete_deliver_custody', true,
  'edit_return_custody', true,
  'delete_return_custody', true
) WHERE `username` = 'admin';

-- ==================== عرض النتائج ====================

-- عرض هيكل الجدول المحدث
DESCRIBE users;

-- عرض المستخدمين مع بيانات الموظفين المرتبطين
SELECT 
    u.id,
    u.username,
    u.employee_code,
    e.full_name as employee_name,
    e.department,
    e.job_title,
    u.created_at
FROM users u
LEFT JOIN employees e ON u.employee_code = e.code
ORDER BY u.id;

-- عرض الموظفين المتاحين (غير المرتبطين بمستخدمين)
SELECT 
    e.code,
    e.full_name,
    e.department,
    e.job_title
FROM employees e
LEFT JOIN users u ON e.code = u.employee_code
WHERE u.employee_code IS NULL 
  AND e.status = 'نشط'
ORDER BY e.full_name;

SELECT 'تم تطبيق جميع التحديثات بنجاح!' as result;
