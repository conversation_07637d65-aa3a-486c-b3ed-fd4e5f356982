# دليل الإعداد السريع - ربط المستخدمين بالموظفين

## 🚀 البدء السريع

### 1. تطبيق التحديثات على قاعدة البيانات

#### الطريقة الأولى: استخدام ملف SQL
```bash
# تشغيل ملف التحديث مباشرة
mysql -u username -p database_name < update_users_employee_link.sql
```

#### الطريقة الثانية: استخدام السكريبت
```bash
# تشغيل الترحيل البرمجي
npm run migrate:user-employee
```

### 2. التحقق من سلامة النظام
```bash
# تشغيل فحص شامل للنظام
npm run validate:system
```

### 3. الإعداد الكامل (ترحيل + تحقق)
```bash
# تشغيل الإعداد الكامل
npm run setup:user-employee
```

## 🔧 ما تم تحديثه

### قاعدة البيانات
- ✅ إضافة حقل `employee_code` لجدول `users`
- ✅ إضافة حقل `permissions` لجدول `users` (إذا لم يكن موجود)
- ✅ إضافة فهارس لتحسين الأداء
- ✅ إضافة مفتاح خارجي للحفاظ على سلامة البيانات

### الخادم (Backend)
- ✅ تحديث `routes/auth.js` لدعم الربط
- ✅ إضافة endpoints جديدة:
  - `GET /api/available-employees` - الموظفين المتاحين
  - `GET /api/all-employees` - جميع الموظفين
  - تحديث `POST /api/users` و `PUT /api/users/:id`

### الواجهة الأمامية (Frontend)
- ✅ تحديث `users.html` - إضافة حقل اختيار الموظف
- ✅ تحديث `users.js` - وظائف الربط والعرض
- ✅ تحديث `users.css` - تنسيقات جديدة

## 🧪 الاختبار

### 1. اختبار سريع
افتح `test_user_employee_integration.html` في المتصفح واختبر:
- الاتصال بالخادم
- عرض المستخدمين مع بيانات الموظفين
- عرض الموظفين المتاحين
- إضافة مستخدم مرتبط بموظف

### 2. اختبار يدوي
1. اذهب إلى صفحة إدارة المستخدمين
2. أضف مستخدم جديد واربطه بموظف
3. تأكد من ظهور بيانات الموظف في الجدول
4. جرب تعديل المستخدم وتغيير الموظف المرتبط

## 🔍 استكشاف الأخطاء

### خطأ "جدول المستخدمين غير موجود"
```bash
# تأكد من وجود قاعدة البيانات والجداول
mysql -u username -p -e "USE database_name; SHOW TABLES;"
```

### خطأ "المفتاح الخارجي"
```bash
# تأكد من وجود جدول الموظفين أولاً
mysql -u username -p -e "USE database_name; DESCRIBE employees;"
```

### خطأ في الصلاحيات
```bash
# تحقق من صلاحيات المستخدم admin
npm run validate:system
```

## 📝 ملاحظات مهمة

### الأمان
- ✅ جميع العمليات تتطلب تسجيل دخول
- ✅ صلاحية `manage_users` مطلوبة لإدارة المستخدمين
- ✅ التحقق من صحة البيانات قبل الحفظ

### الأداء
- ✅ فهارس على `employee_code` لتسريع الاستعلامات
- ✅ استعلامات محسنة مع `LEFT JOIN`
- ✅ تحميل الموظفين حسب الحاجة

### التوافق
- ✅ يعمل مع المستخدمين الموجودين
- ✅ الربط اختياري - لا يؤثر على النظام القديم
- ✅ يمكن إلغاء الربط في أي وقت

## 🆘 الحصول على المساعدة

### سجلات الأخطاء
```bash
# سجلات الخادم
tail -f logs/server.log

# سجلات قاعدة البيانات
mysql -u username -p -e "SHOW ENGINE INNODB STATUS\G"
```

### التحقق من الحالة
```bash
# فحص شامل للنظام
npm run validate:system

# فحص الاتصال بقاعدة البيانات
node -e "require('./migration_user_employee_link.js').runMigration()"
```

### إعادة التعيين
إذا كنت تريد إعادة تطبيق التحديثات:
```sql
-- حذف الحقول المضافة (احذر: سيفقد البيانات)
ALTER TABLE users DROP FOREIGN KEY IF EXISTS fk_users_employee;
ALTER TABLE users DROP INDEX IF EXISTS uk_employee_code;
ALTER TABLE users DROP INDEX IF EXISTS idx_employee_code;
ALTER TABLE users DROP COLUMN IF EXISTS employee_code;

-- ثم أعد تشغيل الترحيل
npm run migrate:user-employee
```

## 📞 الدعم الفني

في حالة وجود مشاكل:
1. تشغيل `npm run validate:system` للحصول على تقرير مفصل
2. فحص سجلات الخادم والمتصفح
3. التأكد من إعدادات قاعدة البيانات في `.env`
4. مراجعة ملف `USER_EMPLOYEE_INTEGRATION_README.md` للتفاصيل الكاملة
