/**
 * نظام الصفحات الموحد للجداول
 * Universal Pagination System for Tables
 */

class PaginationSystem {
  constructor(options = {}) {
    this.currentPage = 1;
    this.itemsPerPage = options.itemsPerPage || 50;
    this.totalPages = 1;
    this.currentDisplayedData = [];
    this.allData = [];
    this.filteredData = [];
    
    // عناصر DOM
    this.tableBody = options.tableBody;
    this.paginationContainer = options.paginationContainer;
    this.itemsPerPageSelect = options.itemsPerPageSelect;
    
    // دوال العرض المخصصة
    this.renderRowFunction = options.renderRowFunction;
    this.noDataMessage = options.noDataMessage || 'لا توجد بيانات للعرض';
    this.columnsCount = options.columnsCount || 6;
    
    // إعداد مستمعي الأحداث
    this.setupEventListeners();
  }

  // إعداد مستمعي الأحداث
  setupEventListeners() {
    if (this.itemsPerPageSelect) {
      this.itemsPerPageSelect.addEventListener('change', (e) => {
        this.itemsPerPage = parseInt(e.target.value);
        this.currentPage = 1;
        this.displayResults(this.currentDisplayedData);
      });
    }
  }

  // حساب عدد الصفحات
  calculateTotalPages(totalItems) {
    return Math.ceil(totalItems / this.itemsPerPage);
  }

  // الحصول على البيانات للصفحة الحالية
  getCurrentPageData(data, page) {
    const startIndex = (page - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return data.slice(startIndex, endIndex);
  }

  // إنشاء أزرار الصفحات
  createPaginationControls() {
    if (!this.paginationContainer) return;

    this.paginationContainer.innerHTML = '';

    if (this.totalPages <= 1) {
      this.paginationContainer.style.display = 'none';
      return;
    }

    this.paginationContainer.style.display = 'flex';

    // زر الصفحة السابقة
    const prevBtn = document.createElement('button');
    prevBtn.className = 'pagination-btn prev-btn';
    prevBtn.innerHTML = '<i class="fas fa-chevron-right"></i> السابق';
    prevBtn.disabled = this.currentPage === 1;
    prevBtn.addEventListener('click', () => this.goToPage(this.currentPage - 1));
    this.paginationContainer.appendChild(prevBtn);

    // أزرار الصفحات
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

    // تعديل startPage إذا كان endPage في النهاية
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // زر الصفحة الأولى إذا لم تكن ظاهرة
    if (startPage > 1) {
      const firstBtn = document.createElement('button');
      firstBtn.className = 'pagination-btn page-btn';
      firstBtn.textContent = '1';
      firstBtn.addEventListener('click', () => this.goToPage(1));
      this.paginationContainer.appendChild(firstBtn);

      if (startPage > 2) {
        const dots = document.createElement('span');
        dots.className = 'pagination-dots';
        dots.textContent = '...';
        this.paginationContainer.appendChild(dots);
      }
    }

    // أزرار الصفحات المرئية
    for (let i = startPage; i <= endPage; i++) {
      const pageBtn = document.createElement('button');
      pageBtn.className = `pagination-btn page-btn ${i === this.currentPage ? 'active' : ''}`;
      pageBtn.textContent = i;
      pageBtn.addEventListener('click', () => this.goToPage(i));
      this.paginationContainer.appendChild(pageBtn);
    }

    // زر الصفحة الأخيرة إذا لم تكن ظاهرة
    if (endPage < this.totalPages) {
      if (endPage < this.totalPages - 1) {
        const dots = document.createElement('span');
        dots.className = 'pagination-dots';
        dots.textContent = '...';
        this.paginationContainer.appendChild(dots);
      }

      const lastBtn = document.createElement('button');
      lastBtn.className = 'pagination-btn page-btn';
      lastBtn.textContent = this.totalPages;
      lastBtn.addEventListener('click', () => this.goToPage(this.totalPages));
      this.paginationContainer.appendChild(lastBtn);
    }

    // زر الصفحة التالية
    const nextBtn = document.createElement('button');
    nextBtn.className = 'pagination-btn next-btn';
    nextBtn.innerHTML = 'التالي <i class="fas fa-chevron-left"></i>';
    nextBtn.disabled = this.currentPage === this.totalPages;
    nextBtn.addEventListener('click', () => this.goToPage(this.currentPage + 1));
    this.paginationContainer.appendChild(nextBtn);

    // عرض معلومات الصفحة
    const pageInfo = document.createElement('div');
    pageInfo.className = 'page-info';
    const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
    const endItem = Math.min(this.currentPage * this.itemsPerPage, this.currentDisplayedData.length);
    pageInfo.textContent = `عرض ${startItem} - ${endItem} من ${this.currentDisplayedData.length} عنصر`;
    this.paginationContainer.appendChild(pageInfo);
  }

  // الانتقال إلى صفحة معينة
  goToPage(page) {
    if (page < 1 || page > this.totalPages) return;
    
    this.currentPage = page;
    const pageData = this.getCurrentPageData(this.currentDisplayedData, this.currentPage);
    this.displayDataInTable(pageData);
    this.createPaginationControls();
  }

  // عرض البيانات في الجدول
  displayDataInTable(data) {
    if (!this.tableBody) return;

    this.tableBody.innerHTML = '';
    
    if (data.length === 0) {
      const row = document.createElement("tr");
      row.innerHTML = `<td colspan="${this.columnsCount}" style="text-align: center; padding: 40px; color: #666; font-style: italic;">${this.noDataMessage}</td>`;
      this.tableBody.appendChild(row);
      return;
    }
    
    data.forEach(item => {
      const row = document.createElement("tr");
      if (this.renderRowFunction) {
        row.innerHTML = this.renderRowFunction(item);
      } else {
        // عرض افتراضي بسيط
        row.innerHTML = `<td>${JSON.stringify(item)}</td>`;
      }
      this.tableBody.appendChild(row);
    });
  }

  // عرض النتائج مع نظام الصفحات
  displayResults(data) {
    this.currentDisplayedData = data;
    this.totalPages = this.calculateTotalPages(data.length);
    this.currentPage = 1; // العودة للصفحة الأولى عند عرض نتائج جديدة
    
    const pageData = this.getCurrentPageData(data, this.currentPage);
    this.displayDataInTable(pageData);
    this.createPaginationControls();
  }

  // تحديث البيانات
  updateData(allData, filteredData = null) {
    this.allData = allData;
    this.filteredData = filteredData || allData;
    this.displayResults(this.filteredData);
  }

  // إعادة تعيين إلى الصفحة الأولى
  resetToFirstPage() {
    this.currentPage = 1;
    this.displayResults(this.currentDisplayedData);
  }

  // الحصول على البيانات الحالية
  getCurrentData() {
    return this.currentDisplayedData;
  }

  // تحديث عدد العناصر في الصفحة
  updateItemsPerPage(newItemsPerPage) {
    this.itemsPerPage = newItemsPerPage;
    this.currentPage = 1;
    this.displayResults(this.currentDisplayedData);
  }
}

// تصدير الكلاس للاستخدام العام
window.PaginationSystem = PaginationSystem;
