/* نظام الصفحات الموحد - Universal Pagination Styles */

/* حاوي نظام الصفحات */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
  padding: 16px 0;
  flex-wrap: wrap;
}

/* أزرار الصفحات */
.pagination-btn {
  background: #ffffff;
  border: 1px solid var(--border-color, #ddd);
  color: var(--text-color, #333);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-family: inherit;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-color, #3498db);
  color: #ffffff;
  border-color: var(--primary-color, #3498db);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-btn.active {
  background: var(--primary-color, #3498db);
  color: #ffffff;
  border-color: var(--primary-color, #3498db);
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

.pagination-btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
  border-color: #e0e0e0;
}

.pagination-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* أزرار السابق والتالي */
.pagination-btn.prev-btn,
.pagination-btn.next-btn {
  padding: 8px 16px;
  font-weight: 500;
  min-width: auto;
}

/* النقاط الفاصلة */
.pagination-dots {
  color: var(--text-color, #333);
  font-weight: bold;
  padding: 0 4px;
  display: flex;
  align-items: center;
  height: 40px;
  user-select: none;
}

/* معلومات الصفحة */
.page-info {
  background: #f8f9fa;
  color: var(--text-color, #333);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-right: 16px;
  border: 1px solid var(--border-color, #ddd);
  white-space: nowrap;
  font-weight: 500;
}

/* عنصر التحكم في عدد العناصر */
.items-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.items-per-page label {
  font-size: 0.9rem;
  color: var(--text-color, #333);
  white-space: nowrap;
  font-weight: 500;
}

.items-per-page select {
  padding: 6px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  background: #ffffff;
  color: var(--text-color, #333);
  font-size: 0.9rem;
  cursor: pointer;
  min-width: 80px;
  font-family: inherit;
}

.items-per-page select:focus {
  outline: none;
  border-color: var(--primary-color, #3498db);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* تحسينات للجداول مع نظام الصفحات */
.table-with-pagination {
  margin-bottom: 0;
}

.table-container-with-pagination {
  background: var(--white, #fff);
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.1);
  padding: 24px 16px;
  margin-top: 24px;
  overflow-x: auto;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
  .pagination-container {
    gap: 4px;
    padding: 12px 0;
    flex-direction: column;
  }

  .pagination-btn {
    padding: 6px 8px;
    font-size: 0.8rem;
    min-width: 32px;
    height: 32px;
  }

  .pagination-btn.prev-btn,
  .pagination-btn.next-btn {
    padding: 6px 12px;
  }

  .page-info {
    font-size: 0.8rem;
    padding: 6px 12px;
    margin-right: 8px;
    order: -1;
    width: 100%;
    text-align: center;
    margin-bottom: 8px;
    margin-right: 0;
  }

  .pagination-container > *:not(.page-info) {
    order: 1;
  }

  .items-per-page {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-left: 0;
    margin-top: 8px;
    width: 100%;
  }

  .items-per-page label {
    font-size: 0.8rem;
  }

  .items-per-page select {
    font-size: 0.8rem;
    padding: 4px 8px;
    min-width: 60px;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .pagination-btn {
    padding: 4px 6px;
    font-size: 0.75rem;
    min-width: 28px;
    height: 28px;
  }

  .pagination-btn.prev-btn,
  .pagination-btn.next-btn {
    padding: 4px 8px;
  }

  .page-info {
    font-size: 0.75rem;
    padding: 4px 8px;
  }

  .pagination-dots {
    height: 28px;
  }
}

/* تأثيرات إضافية */
.pagination-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.pagination-container.loading .pagination-btn {
  opacity: 0.6;
  pointer-events: none;
}

/* تنسيقات خاصة للثيمات المختلفة */
.dark-theme .pagination-btn {
  background: #2c3e50;
  border-color: #34495e;
  color: #ecf0f1;
}

.dark-theme .pagination-btn:hover:not(:disabled) {
  background: var(--primary-color, #3498db);
  border-color: var(--primary-color, #3498db);
}

.dark-theme .page-info {
  background: #34495e;
  color: #ecf0f1;
  border-color: #2c3e50;
}

.dark-theme .items-per-page select {
  background: #2c3e50;
  border-color: #34495e;
  color: #ecf0f1;
}

/* تحسينات الأداء */
.pagination-container * {
  will-change: transform;
}

.pagination-btn {
  backface-visibility: hidden;
}

/* رسوم متحركة ناعمة */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pagination-container {
  animation: fadeIn 0.3s ease-out;
}

/* تنسيقات للطباعة */
@media print {
  .pagination-container {
    display: none !important;
  }
  
  .items-per-page {
    display: none !important;
  }
}
