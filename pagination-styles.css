/* نظام الصفحات الموحد - Universal Pagination Styles */
/* هذا الملف يجب أن يتم تحميله في النهاية لضمان الأولوية */

/* إعادة تعيين التنسيقات المتعارضة */
* {
  box-sizing: border-box;
}

/* تنسيقات قوية لحل التعارضات */
div.table-controls,
div.filter-actions,
div.logs-actions,
div.actions-bar {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 12px !important;
  margin-bottom: 16px !important;
}

div.table-controls div.items-per-page,
div.filter-actions div.items-per-page,
div.logs-actions div.items-per-page,
div.actions-bar div.items-per-page {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  margin-left: auto !important;
  flex-shrink: 0 !important;
}

/* حاوي نظام الصفحات */
.pagination-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 8px !important;
  margin-top: 20px !important;
  padding: 16px 0 !important;
  flex-wrap: wrap !important;
  clear: both !important;
}

/* أزرار الصفحات */
.pagination-btn {
  background: #ffffff;
  border: 1px solid var(--border-color, #ddd);
  color: var(--text-color, #333);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-family: inherit;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-color, #3498db);
  color: #ffffff;
  border-color: var(--primary-color, #3498db);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-btn.active {
  background: var(--primary-color, #3498db);
  color: #ffffff;
  border-color: var(--primary-color, #3498db);
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

.pagination-btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
  border-color: #e0e0e0;
}

.pagination-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* أزرار السابق والتالي */
.pagination-btn.prev-btn,
.pagination-btn.next-btn {
  padding: 8px 16px;
  font-weight: 500;
  min-width: auto;
}

/* النقاط الفاصلة */
.pagination-dots {
  color: var(--text-color, #333);
  font-weight: bold;
  padding: 0 4px;
  display: flex;
  align-items: center;
  height: 40px;
  user-select: none;
}

/* معلومات الصفحة */
.page-info {
  background: #f8f9fa;
  color: var(--text-color, #333);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-right: 16px;
  border: 1px solid var(--border-color, #ddd);
  white-space: nowrap;
  font-weight: 500;
}

/* عنصر التحكم في عدد العناصر - تنسيقات أكثر تحديداً */
.table-controls .items-per-page,
.filter-actions .items-per-page,
.logs-actions .items-per-page,
.actions-bar .items-per-page {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  margin-left: auto !important;
  flex-shrink: 0 !important;
}

.table-controls .items-per-page label,
.filter-actions .items-per-page label,
.logs-actions .items-per-page label,
.actions-bar .items-per-page label {
  font-size: 0.9rem !important;
  color: var(--text-color, #333) !important;
  white-space: nowrap !important;
  font-weight: 500 !important;
  margin: 0 !important;
}

.table-controls .items-per-page select,
.filter-actions .items-per-page select,
.logs-actions .items-per-page select,
.actions-bar .items-per-page select {
  padding: 6px 12px !important;
  border: 1px solid var(--border-color, #ddd) !important;
  border-radius: 6px !important;
  background: #ffffff !important;
  color: var(--text-color, #333) !important;
  font-size: 0.9rem !important;
  cursor: pointer !important;
  min-width: 80px !important;
  font-family: inherit !important;
  margin: 0 !important;
}

.table-controls .items-per-page select:focus,
.filter-actions .items-per-page select:focus,
.logs-actions .items-per-page select:focus,
.actions-bar .items-per-page select:focus {
  outline: none !important;
  border-color: var(--primary-color, #3498db) !important;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2) !important;
}

/* تحسينات للجداول مع نظام الصفحات */
.table-with-pagination {
  margin-bottom: 0;
}

.table-container-with-pagination {
  background: var(--white, #fff);
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.1);
  padding: 24px 16px;
  margin-top: 24px;
  overflow-x: auto;
}

/* إصلاح التعارضات مع shared-styles.css */
.table-controls {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 16px !important;
  flex-wrap: wrap !important;
  gap: 12px !important;
}

.filter-actions {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 12px !important;
  flex-wrap: wrap !important;
}

.logs-actions {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.actions-bar {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 20px !important;
  flex-wrap: wrap !important;
  gap: 12px !important;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
  .pagination-container {
    gap: 4px;
    padding: 12px 0;
    flex-direction: column;
  }

  .pagination-btn {
    padding: 6px 8px;
    font-size: 0.8rem;
    min-width: 32px;
    height: 32px;
  }

  .pagination-btn.prev-btn,
  .pagination-btn.next-btn {
    padding: 6px 12px;
  }

  .page-info {
    font-size: 0.8rem;
    padding: 6px 12px;
    margin-right: 8px;
    order: -1;
    width: 100%;
    text-align: center;
    margin-bottom: 8px;
    margin-right: 0;
  }

  .pagination-container > *:not(.page-info) {
    order: 1;
  }

  .table-controls .items-per-page,
  .filter-actions .items-per-page,
  .logs-actions .items-per-page,
  .actions-bar .items-per-page {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 4px !important;
    margin-left: 0 !important;
    margin-top: 8px !important;
    width: 100% !important;
  }

  .table-controls .items-per-page label,
  .filter-actions .items-per-page label,
  .logs-actions .items-per-page label,
  .actions-bar .items-per-page label {
    font-size: 0.8rem !important;
  }

  .table-controls .items-per-page select,
  .filter-actions .items-per-page select,
  .logs-actions .items-per-page select,
  .actions-bar .items-per-page select {
    font-size: 0.8rem !important;
    padding: 4px 8px !important;
    min-width: 60px !important;
    width: 100% !important;
  }

  .table-controls,
  .filter-actions,
  .actions-bar {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
  }
}

@media (max-width: 480px) {
  .pagination-btn {
    padding: 4px 6px;
    font-size: 0.75rem;
    min-width: 28px;
    height: 28px;
  }

  .pagination-btn.prev-btn,
  .pagination-btn.next-btn {
    padding: 4px 8px;
  }

  .page-info {
    font-size: 0.75rem;
    padding: 4px 8px;
  }

  .pagination-dots {
    height: 28px;
  }
}

/* تأثيرات إضافية */
.pagination-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.pagination-container.loading .pagination-btn {
  opacity: 0.6;
  pointer-events: none;
}

/* تنسيقات خاصة للثيمات المختلفة */
.dark-theme .pagination-btn {
  background: #2c3e50;
  border-color: #34495e;
  color: #ecf0f1;
}

.dark-theme .pagination-btn:hover:not(:disabled) {
  background: var(--primary-color, #3498db);
  border-color: var(--primary-color, #3498db);
}

.dark-theme .page-info {
  background: #34495e;
  color: #ecf0f1;
  border-color: #2c3e50;
}

.dark-theme .items-per-page select {
  background: #2c3e50;
  border-color: #34495e;
  color: #ecf0f1;
}

/* تحسينات الأداء */
.pagination-container * {
  will-change: transform;
}

.pagination-btn {
  backface-visibility: hidden;
}

/* رسوم متحركة ناعمة */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pagination-container {
  animation: fadeIn 0.3s ease-out;
}

/* تنسيقات للطباعة */
@media print {
  .pagination-container {
    display: none !important;
  }
  
  .items-per-page {
    display: none !important;
  }
}
