# دليل ربط المستخدمين بالموظفين

## نظرة عامة
تم تطوير نظام لربط المستخدمين في النظام بالموظفين الموجودين في قاعدة البيانات. هذا يسمح بإدارة أفضل للصلاحيات وربط كل مستخدم بموظف محدد.

## الميزات الجديدة

### 1. ربط المستخدم بالموظف
- كل مستخدم يمكن ربطه بموظف واحد فقط
- الموظف الواحد لا يمكن ربطه بأكثر من مستخدم
- الربط اختياري - يمكن إنشاء مستخدم بدون ربطه بموظف

### 2. عرض محسن للمستخدمين
- جدول المستخدمين يعرض الآن:
  - اسم المستخدم
  - اسم الموظف المرتبط
  - قسم الموظف
  - تاريخ الإنشاء
  - الصلاحيات

### 3. واجهة محسنة لإدارة المستخدمين
- قائمة اختيار الموظفين عند إضافة/تعديل المستخدم
- عرض الموظفين المتاحين فقط (غير المرتبطين) عند الإضافة
- عرض جميع الموظفين عند التعديل مع إمكانية تغيير الربط

## التحديثات المطلوبة

### 1. قاعدة البيانات
قم بتشغيل الملف `update_users_employee_link.sql` لتطبيق التحديثات على قاعدة البيانات:

```sql
-- تشغيل الملف
mysql -u username -p database_name < update_users_employee_link.sql
```

أو قم بنسخ ولصق محتوى الملف في phpMyAdmin أو أي أداة إدارة قواعد البيانات.

### 2. الخادم (Backend)
تم تحديث الملفات التالية:
- `routes/auth.js` - إضافة دعم للحقل الجديد وendpoints جديدة
- `mysql table code.sql` - تحديث هيكل الجدول

### 3. الواجهة الأمامية (Frontend)
تم تحديث الملفات التالية:
- `users.html` - إضافة حقل اختيار الموظف وتحديث الجدول
- `users.js` - إضافة وظائف تحميل الموظفين والربط
- `users.css` - تنسيقات جديدة للعناصر المضافة

## كيفية الاستخدام

### 1. إضافة مستخدم جديد
1. اذهب إلى صفحة إدارة المستخدمين
2. اضغط على "إضافة مستخدم جديد"
3. أدخل اسم المستخدم وكلمة المرور
4. اختر موظف من القائمة (اختياري)
5. حدد الصلاحيات المطلوبة
6. احفظ المستخدم

### 2. تعديل مستخدم موجود
1. اضغط على زر التعديل بجانب المستخدم
2. يمكنك تغيير الموظف المرتبط أو إلغاء الربط
3. احفظ التغييرات

### 3. عرض المستخدمين
- الجدول يعرض الآن اسم الموظف المرتبط والقسم
- المستخدمين غير المرتبطين يظهر لهم "غير مرتبط"

## API Endpoints الجديدة

### 1. جلب الموظفين المتاحين
```
GET /api/available-employees
```
يعيد قائمة بالموظفين النشطين غير المرتبطين بمستخدمين

### 2. جلب جميع الموظفين
```
GET /api/all-employees
```
يعيد قائمة بجميع الموظفين النشطين (للتعديل)

### 3. إضافة مستخدم مع ربط موظف
```
POST /api/users
{
  "username": "اسم_المستخدم",
  "password": "كلمة_المرور",
  "employee_code": 123,
  "permissions": {...}
}
```

### 4. تحديث مستخدم مع تغيير الربط
```
PUT /api/users/:id
{
  "username": "اسم_المستخدم",
  "employee_code": 456,
  "permissions": {...}
}
```

## الاختبار

### 1. ملف الاختبار
استخدم الملف `test_user_employee_integration.html` لاختبار النظام:
1. افتح الملف في المتصفح
2. تأكد من تسجيل الدخول أولاً للحصول على التوكن
3. اختبر جميع الوظائف

### 2. اختبارات يدوية
1. **اختبار الربط**: أضف مستخدم جديد واربطه بموظف
2. **اختبار منع التكرار**: حاول ربط موظف مرتبط بالفعل
3. **اختبار التحديث**: غير الموظف المرتبط بمستخدم موجود
4. **اختبار العرض**: تأكد من ظهور بيانات الموظف في الجدول

## ملاحظات مهمة

### 1. الأمان
- يتم التحقق من وجود الموظف قبل الربط
- يتم منع ربط موظف واحد بأكثر من مستخدم
- جميع العمليات تتطلب صلاحية `manage_users`

### 2. التوافق مع النظام القديم
- المستخدمين الموجودين لن يتأثروا
- يمكن ترك المستخدمين بدون ربط بموظف
- النظام يعمل مع وبدون الربط

### 3. قاعدة البيانات
- تم إضافة `FOREIGN KEY` للحفاظ على سلامة البيانات
- عند حذف موظف، يتم إلغاء ربطه بالمستخدم تلقائياً
- الفهارس تحسن أداء الاستعلامات

## استكشاف الأخطاء

### 1. خطأ "الموظف مرتبط بمستخدم آخر"
- تأكد من أن الموظف غير مرتبط بمستخدم آخر
- استخدم endpoint `/available-employees` للحصول على الموظفين المتاحين

### 2. خطأ "الموظف غير موجود"
- تأكد من أن كود الموظف صحيح
- تأكد من أن الموظف نشط (status = 'نشط')

### 3. مشاكل في قاعدة البيانات
- تأكد من تشغيل ملف التحديث `update_users_employee_link.sql`
- تحقق من وجود جدول `employees` قبل إضافة المفتاح الخارجي

## الدعم
في حالة وجود مشاكل، تحقق من:
1. سجلات الخادم (server logs)
2. وحدة تحكم المتصفح (browser console)
3. ملف الاختبار للتأكد من عمل النظام
