/* جدول عصري أزرق ثابت مع فواصل واضحة لقاعدة بيانات الموظفين */

.employee-table-container {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 4px 24px var(--shadow-color);
  padding: 24px 16px;
  margin-top: 24px;
  overflow-x: auto;
}

/* تم نقل تنسيقات الجداول إلى shared-styles.css */

/* تم نقل تنسيقات الأزرار إلى shared-styles.css */

/* تم نقل تنسيقات زر العرض إلى shared-styles.css */

@media (max-width: 700px) {
  .employee-table thead th,
  .employee-table tbody td {
    font-size: 0.95rem;
    padding: 8px 4px;
  }

  .employee-table-container {
    padding: 8px 2px;
  }

  .items-per-page {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-left: 0;
    margin-top: 8px;
  }

  .items-per-page label {
    font-size: 0.8rem;
  }

  .items-per-page select {
    font-size: 0.8rem;
    padding: 4px 8px;
    min-width: 60px;
  }
}

/* تم نقل تنسيقات .table-controls إلى shared-styles.css */

.items-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.items-per-page label {
  font-size: 0.9rem;
  color: var(--text-color);
  white-space: nowrap;
}

.items-per-page select {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: #ffffff;
  color: var(--text-color);
  font-size: 0.9rem;
  cursor: pointer;
  min-width: 80px;
}

.items-per-page select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.export-btn {
  background: var(--primary-color);
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s;
}

.export-btn:hover {
  background: var(--primary-dark);
}

.delete-all {
  background-color: var(--danger-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.delete-all:hover {
  background-color: var(--danger-dark);
}

.calculate-leave {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin-right: 10px;
}

.calculate-leave:hover {
  background-color: #218838;
}

.actions-bar {
  display: flex;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  margin: 5% auto;
  padding: 0;
  border-radius: 12px;
  width: 80%;
  max-width: 600px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.close {
  color: var(--text-color);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: var(--danger-color);
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Field Selection Styles */
.field-selection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.field-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.field-checkbox label {
  cursor: pointer;
  font-size: 1rem;
}

/* Button Styles */
.confirm-btn, .cancel-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s;
}

.confirm-btn {
  background: var(--primary-color);
  color: #ffffff;
}

.confirm-btn:hover {
  background: var(--primary-dark);
}

.cancel-btn {
  background: #f0f0f0;
  color: #333;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

/* نظام الصفحات */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
  padding: 16px 0;
  flex-wrap: wrap;
}

.pagination-btn {
  background: #ffffff;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
  font-weight: bold;
}

.pagination-btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
  border-color: #e0e0e0;
}

.pagination-btn.prev-btn,
.pagination-btn.next-btn {
  padding: 8px 16px;
  font-weight: 500;
}

.pagination-dots {
  color: var(--text-color);
  font-weight: bold;
  padding: 0 4px;
  display: flex;
  align-items: center;
  height: 40px;
}

.page-info {
  background: #f8f9fa;
  color: var(--text-color);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-right: 16px;
  border: 1px solid var(--border-color);
  white-space: nowrap;
}

@media (max-width: 700px) {
  .modal-content {
    width: 95%;
    margin: 10% auto;
  }

  .fields-grid {
    grid-template-columns: 1fr;
  }

  .pagination-container {
    gap: 4px;
    padding: 12px 0;
  }

  .pagination-btn {
    padding: 6px 8px;
    font-size: 0.8rem;
    min-width: 32px;
    height: 32px;
  }

  .pagination-btn.prev-btn,
  .pagination-btn.next-btn {
    padding: 6px 12px;
  }

  .page-info {
    font-size: 0.8rem;
    padding: 6px 12px;
    margin-right: 8px;
    order: -1;
    width: 100%;
    text-align: center;
    margin-bottom: 8px;
  }

  .pagination-container {
    flex-direction: column;
  }

  .pagination-container > *:not(.page-info) {
    order: 1;
  }
}
