# دليل المطور لنظام الصفحات الموحد
# Developer Guide for Universal Pagination System

## نظرة عامة
تم تطوير نظام صفحات موحد وقابل لإعادة الاستخدام لجميع الجداول في المشروع. يوفر النظام واجهة برمجية بسيطة ومرنة مع تصميم متجاوب وأداء محسن.

## الملفات الأساسية

### 1. pagination-system.js
الملف الأساسي الذي يحتوي على كلاس `PaginationSystem`:

```javascript
class PaginationSystem {
  constructor(options = {}) {
    this.currentPage = 1;
    this.itemsPerPage = options.itemsPerPage || 50;
    this.totalPages = 1;
    this.currentDisplayedData = [];
    // ... باقي الخصائص
  }
}
```

### 2. pagination-styles.css
ملف التصميم الموحد لجميع عناصر نظام الصفحات.

### 3. universal-pagination-integration.js
ملف التكامل الذي يحتوي على دوال العرض المخصصة لكل نوع جدول.

### 4. apply-pagination-to-all-tables.js
ملف إعداد شامل يحتوي على تكوين جميع الجداول.

## كيفية إضافة نظام الصفحات لجدول جديد

### الخطوة 1: إضافة المراجع في HTML
```html
<link rel="stylesheet" href="pagination-styles.css" />
<script src="pagination-system.js" defer></script>
<script src="universal-pagination-integration.js" defer></script>
```

### الخطوة 2: إضافة عناصر التحكم
```html
<div class="table-controls">
  <div class="items-per-page">
    <label for="myTableItemsPerPageSelect">عدد العناصر في الصفحة:</label>
    <select id="myTableItemsPerPageSelect">
      <option value="25">25</option>
      <option value="50" selected>50</option>
      <option value="100">100</option>
      <option value="200">200</option>
    </select>
  </div>
</div>
```

### الخطوة 3: إضافة حاوي نظام الصفحات
```html
<table id="myTable">
  <!-- محتوى الجدول -->
</table>

<!-- نظام الصفحات -->
<div class="pagination-container" id="myTablePaginationContainer"></div>
```

### الخطوة 4: إنشاء مثيل نظام الصفحات
```javascript
const pagination = new PaginationSystem({
  tableBody: document.querySelector('#myTable tbody'),
  paginationContainer: document.getElementById('myTablePaginationContainer'),
  itemsPerPageSelect: document.getElementById('myTableItemsPerPageSelect'),
  renderRowFunction: (item) => `<td>${item.name}</td><td>${item.value}</td>`,
  columnsCount: 2,
  noDataMessage: 'لا توجد بيانات للعرض'
});
```

### الخطوة 5: عرض البيانات
```javascript
// تحميل البيانات
const data = await fetchDataFromAPI();

// عرض البيانات مع نظام الصفحات
pagination.displayResults(data);
```

## دوال العرض المخصصة

### مثال لدالة عرض صف موظف:
```javascript
function renderEmployeeRow(emp) {
  const displayValue = (value) => value === null ? '-' : value;
  
  return `<td>${displayValue(emp.code)}</td>
<td>${displayValue(emp.full_name)}</td>
<td>${displayValue(emp.department)}</td>
<td>
  <button class="view-btn" data-id="${emp.code}">عرض</button>
  <button class="edit-btn" data-id="${emp.code}">تعديل</button>
</td>`;
}
```

### مثال لدالة عرض صف تقرير:
```javascript
function renderReportRow(report) {
  return `<td>${report.date}</td>
<td>${report.description}</td>
<td>${report.amount}</td>`;
}
```

## الخصائص والدوال المتاحة

### الخصائص الأساسية:
- `currentPage`: الصفحة الحالية
- `itemsPerPage`: عدد العناصر في الصفحة
- `totalPages`: العدد الإجمالي للصفحات
- `currentDisplayedData`: البيانات المعروضة حالياً

### الدوال الأساسية:
- `displayResults(data)`: عرض البيانات مع نظام الصفحات
- `goToPage(page)`: الانتقال لصفحة معينة
- `updateData(allData, filteredData)`: تحديث البيانات
- `resetToFirstPage()`: العودة للصفحة الأولى
- `getCurrentData()`: الحصول على البيانات الحالية

## التكامل مع البحث والفلترة

```javascript
// دالة البحث
function searchData(searchTerm) {
  const filteredData = allData.filter(item => 
    item.name.includes(searchTerm) || 
    item.description.includes(searchTerm)
  );
  
  // عرض النتائج المفلترة
  pagination.displayResults(filteredData);
}

// دالة الفلترة
function filterData(filterCriteria) {
  const filteredData = allData.filter(item => 
    item.category === filterCriteria.category
  );
  
  pagination.displayResults(filteredData);
}
```

## معالجة الأحداث

```javascript
// معالجة النقر على أزرار الإجراءات
document.addEventListener('click', (e) => {
  if (e.target.classList.contains('edit-btn')) {
    const id = e.target.dataset.id;
    editItem(id);
  }
  
  if (e.target.classList.contains('delete-btn')) {
    const id = e.target.dataset.id;
    deleteItem(id);
  }
});
```

## التخصيص والتصميم

### تخصيص الألوان:
```css
:root {
  --primary-color: #3498db;
  --primary-dark: #2980b9;
  --border-color: #ddd;
  --text-color: #333;
}
```

### تخصيص الأحجام:
```css
.pagination-btn {
  min-width: 40px;
  height: 40px;
  padding: 8px 12px;
}
```

## أفضل الممارسات

### 1. تسمية العناصر:
- استخدم أسماء واضحة ومميزة للمعرفات
- اتبع نمط التسمية: `{tableName}ItemsPerPageSelect`

### 2. معالجة البيانات:
- تأكد من معالجة القيم الفارغة (`null`, `undefined`)
- استخدم دوال `displayValue` لعرض القيم

### 3. الأداء:
- تجنب إعادة إنشاء نظام الصفحات في كل مرة
- احفظ مرجع للمثيل للاستخدام لاحقاً

### 4. إمكانية الوصول:
- استخدم تسميات واضحة للأزرار
- تأكد من دعم لوحة المفاتيح

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. لا يظهر نظام الصفحات:
```javascript
// تأكد من وجود العناصر
const tableBody = document.querySelector('#myTable tbody');
const container = document.getElementById('paginationContainer');

if (!tableBody || !container) {
  console.error('العناصر المطلوبة غير موجودة');
}
```

#### 2. البيانات لا تظهر:
```javascript
// تأكد من صحة دالة العرض
function renderRow(item) {
  console.log('Rendering item:', item);
  return `<td>${item.name}</td>`;
}
```

#### 3. أزرار التنقل لا تعمل:
```javascript
// تأكد من ربط الأحداث بشكل صحيح
pagination.createPaginationControls();
```

## مثال شامل

```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <link rel="stylesheet" href="pagination-styles.css" />
  <script src="pagination-system.js" defer></script>
</head>
<body>
  <div class="table-controls">
    <div class="items-per-page">
      <label for="itemsSelect">عدد العناصر:</label>
      <select id="itemsSelect">
        <option value="25">25</option>
        <option value="50" selected>50</option>
      </select>
    </div>
  </div>
  
  <table id="dataTable">
    <thead>
      <tr><th>الاسم</th><th>القيمة</th></tr>
    </thead>
    <tbody></tbody>
  </table>
  
  <div class="pagination-container" id="paginationContainer"></div>

  <script>
    const pagination = new PaginationSystem({
      tableBody: document.querySelector('#dataTable tbody'),
      paginationContainer: document.getElementById('paginationContainer'),
      itemsPerPageSelect: document.getElementById('itemsSelect'),
      renderRowFunction: (item) => `<td>${item.name}</td><td>${item.value}</td>`,
      columnsCount: 2
    });
    
    // تحميل وعرض البيانات
    const data = [
      {name: 'عنصر 1', value: 'قيمة 1'},
      {name: 'عنصر 2', value: 'قيمة 2'},
      // ... المزيد من البيانات
    ];
    
    pagination.displayResults(data);
  </script>
</body>
</html>
```

## الدعم والصيانة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من وحدة التحكم للأخطاء
3. تأكد من تحديث جميع الملفات المطلوبة
4. اختبر على متصفحات مختلفة
