/**
 * ملف الترحيل لربط المستخدمين بالموظفين
 * يمكن تشغيل هذا الملف لتطبيق التحديثات على قاعدة البيانات برمجياً
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// إعدادات قاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'employee_system',
  charset: 'utf8mb4'
};

async function runMigration() {
  let connection;
  
  try {
    console.log('🔄 بدء ترحيل قاعدة البيانات...');
    
    // إنشاء الاتصال
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // التحقق من وجود جدول المستخدمين
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'users'"
    );
    
    if (tables.length === 0) {
      console.log('❌ جدول المستخدمين غير موجود');
      return;
    }

    // التحقق من وجود حقل employee_code
    const [employeeCodeColumns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'employee_code'"
    );
    
    if (employeeCodeColumns.length === 0) {
      console.log('🔄 إضافة حقل employee_code...');
      await connection.execute(
        "ALTER TABLE users ADD COLUMN employee_code int DEFAULT NULL COMMENT 'كود الموظف المرتبط بالمستخدم' AFTER password"
      );
      console.log('✅ تم إضافة حقل employee_code');
    } else {
      console.log('ℹ️ حقل employee_code موجود بالفعل');
    }

    // التحقق من وجود حقل permissions
    const [permissionsColumns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'permissions'"
    );
    
    if (permissionsColumns.length === 0) {
      console.log('🔄 إضافة حقل permissions...');
      await connection.execute(
        "ALTER TABLE users ADD COLUMN permissions JSON DEFAULT NULL COMMENT 'صلاحيات المستخدم' AFTER employee_code"
      );
      console.log('✅ تم إضافة حقل permissions');
    } else {
      console.log('ℹ️ حقل permissions موجود بالفعل');
    }

    // التحقق من وجود الفهرس الفريد
    const [uniqueIndexes] = await connection.execute(
      "SHOW INDEX FROM users WHERE Key_name = 'uk_employee_code'"
    );
    
    if (uniqueIndexes.length === 0) {
      console.log('🔄 إضافة الفهرس الفريد على employee_code...');
      try {
        await connection.execute(
          "ALTER TABLE users ADD UNIQUE KEY uk_employee_code (employee_code)"
        );
        console.log('✅ تم إضافة الفهرس الفريد');
      } catch (error) {
        console.log('⚠️ تحذير: لم يتم إضافة الفهرس الفريد:', error.message);
      }
    } else {
      console.log('ℹ️ الفهرس الفريد موجود بالفعل');
    }

    // التحقق من وجود الفهرس العادي
    const [normalIndexes] = await connection.execute(
      "SHOW INDEX FROM users WHERE Key_name = 'idx_employee_code'"
    );
    
    if (normalIndexes.length === 0) {
      console.log('🔄 إضافة الفهرس على employee_code...');
      try {
        await connection.execute(
          "ALTER TABLE users ADD KEY idx_employee_code (employee_code)"
        );
        console.log('✅ تم إضافة الفهرس');
      } catch (error) {
        console.log('⚠️ تحذير: لم يتم إضافة الفهرس:', error.message);
      }
    } else {
      console.log('ℹ️ الفهرس موجود بالفعل');
    }

    // التحقق من وجود جدول الموظفين قبل إضافة المفتاح الخارجي
    const [employeeTables] = await connection.execute(
      "SHOW TABLES LIKE 'employees'"
    );
    
    if (employeeTables.length > 0) {
      // التحقق من وجود المفتاح الخارجي
      const [foreignKeys] = await connection.execute(
        "SELECT CONSTRAINT_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE CONSTRAINT_SCHEMA = ? AND TABLE_NAME = 'users' AND CONSTRAINT_NAME = 'fk_users_employee'",
        [dbConfig.database]
      );
      
      if (foreignKeys.length === 0) {
        console.log('🔄 إضافة المفتاح الخارجي...');
        try {
          await connection.execute(
            "ALTER TABLE users ADD CONSTRAINT fk_users_employee FOREIGN KEY (employee_code) REFERENCES employees (code) ON DELETE SET NULL ON UPDATE CASCADE"
          );
          console.log('✅ تم إضافة المفتاح الخارجي');
        } catch (error) {
          console.log('⚠️ تحذير: لم يتم إضافة المفتاح الخارجي:', error.message);
        }
      } else {
        console.log('ℹ️ المفتاح الخارجي موجود بالفعل');
      }
    } else {
      console.log('⚠️ جدول الموظفين غير موجود - تم تخطي إضافة المفتاح الخارجي');
    }

    // تحديث صلاحيات المستخدم admin
    console.log('🔄 تحديث صلاحيات المستخدم admin...');
    const adminPermissions = {
      can_view: true,
      can_add: true,
      can_edit: true,
      can_delete: true,
      view_employees: true,
      view_vacations: true,
      view_contributions: true,
      view_rewards_penalties: true,
      view_rewards_list: true,
      add_reward: true,
      export_rewards: true,
      view_deductions_list: true,
      add_deduction: true,
      export_deductions: true,
      view_custody: true,
      view_evaluation: true,
      view_training: true,
      add_training: true,
      edit_training: true,
      delete_training: true,
      view_training_reports: true,
      view_resignations: true,
      add_resignation: true,
      edit_resignation: true,
      delete_resignation: true,
      view_resignation_reports: true,
      view_salary_advances: true,
      add_salary_advance: true,
      edit_salary_advance: true,
      delete_salary_advance: true,
      view_salary_advance_reports: true,
      view_extra_hours: true,
      add_extra_hour: true,
      edit_extra_hour: true,
      delete_extra_hour: true,
      view_extra_hour_reports: true,
      view_import: true,
      manage_users: true,
      edit_deliver_custody: true,
      delete_deliver_custody: true,
      edit_return_custody: true,
      delete_return_custody: true
    };

    await connection.execute(
      "UPDATE users SET permissions = ? WHERE username = 'admin'",
      [JSON.stringify(adminPermissions)]
    );
    console.log('✅ تم تحديث صلاحيات المستخدم admin');

    // عرض النتائج
    console.log('\n📊 نتائج الترحيل:');
    
    // عرض هيكل الجدول
    const [columns] = await connection.execute("DESCRIBE users");
    console.log('\n🏗️ هيكل جدول المستخدمين:');
    columns.forEach(column => {
      console.log(`  - ${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(not null)'}`);
    });

    // عرض المستخدمين مع بيانات الموظفين
    const [users] = await connection.execute(`
      SELECT 
        u.id,
        u.username,
        u.employee_code,
        e.full_name as employee_name,
        e.department,
        u.created_at
      FROM users u
      LEFT JOIN employees e ON u.employee_code = e.code
      ORDER BY u.id
    `);
    
    console.log('\n👥 المستخدمين الحاليين:');
    if (users.length > 0) {
      users.forEach(user => {
        console.log(`  - ${user.username} (ID: ${user.id}) -> ${user.employee_name || 'غير مرتبط'} ${user.department ? `(${user.department})` : ''}`);
      });
    } else {
      console.log('  لا يوجد مستخدمين');
    }

    // عرض الموظفين المتاحين
    if (employeeTables.length > 0) {
      const [availableEmployees] = await connection.execute(`
        SELECT 
          e.code,
          e.full_name,
          e.department
        FROM employees e
        LEFT JOIN users u ON e.code = u.employee_code
        WHERE u.employee_code IS NULL 
          AND e.status = 'نشط'
        ORDER BY e.full_name
        LIMIT 5
      `);
      
      console.log('\n🆓 الموظفين المتاحين للربط (أول 5):');
      if (availableEmployees.length > 0) {
        availableEmployees.forEach(employee => {
          console.log(`  - ${employee.full_name} (${employee.code}) - ${employee.department}`);
        });
      } else {
        console.log('  جميع الموظفين مرتبطين أو لا يوجد موظفين نشطين');
      }
    }

    console.log('\n🎉 تم إكمال الترحيل بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في الترحيل:', error.message);
    console.error(error.stack);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الترحيل إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };
