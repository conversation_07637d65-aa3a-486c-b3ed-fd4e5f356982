<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام العهد</title>
  <link rel="stylesheet" href="shared-styles.css" />
  <link rel="stylesheet" href="style.css" />
  <!-- <link rel="stylesheet" href="custody.css"> -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <link rel="stylesheet" href="pagination-styles.css" />
  <script src="permissions.js" defer></script>
  <script src="pagination-system.js" defer></script>
  <script src="pagination-styles-fix.js" defer></script>
</head>
<body class="custody-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="custody-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة العهد</span>
    </a>
  </div>





  <div class="main-content full-width" id="mainContent">
    <h1>نظام العهد</h1>



    <!-- إضافة عهدة -->
    <div class="tab-content" id="add-custody" style="display: none;">
      <form id="addCustodyForm" class="custody-form form-grid">
        <!-- إخفاء حقل كود العهدة من خانة الإدخال -->
        <input type="hidden" id="custodyCode" name="custodyCode">
        <div class="form-group">
          <label for="custodyName">اسم العهدة:</label>
          <input type="text" id="custodyName" name="custodyName" required>
        </div>
        <div class="form-group">
          <label for="custodyType">نوع العهدة:</label>
          <input type="text" id="custodyType" name="custodyType" required>
        </div>
        <div class="form-group">
          <label for="custodyStatus">حالة العهدة:</label>
          <select id="custodyStatus" required>
              <option value="">اختر الحالة</option>
              <option value="جديدة">جديدة</option>
              <option value="مستعمل">مستعمل</option>
              <option value="صيانة">صيانة</option>
            </select>
        </div>
        <div class="form-group">
          <label for="custodyQuantity">العدد:</label>
          <input type="number" id="custodyQuantity" name="custodyQuantity" min="1" required>
        </div>
        <div class="form-actions" style="grid-column: span 4;">
          <button type="submit" class="save-btn">حفظ العهدة</button>
          <button type="button" class="reset-btn">إعادة تعيين</button>
        </div>
      </form>

      <div class="custody-table-container">
        <h3>قائمة العهد</h3>
        <div class="search-container" style="margin-bottom: 15px;">
          <div class="search-box" style="position: relative; width: 100%;">
            <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #666;"></i>
            <input type="text" id="searchCustody" placeholder="بحث في العهد..." style="width: 100%; padding: 10px 35px 10px 10px; border-radius: 8px; border: 1px solid #ddd; font-size: 16px; transition: all 0.3s ease;">
          </div>
        </div>
        <div class="table-controls" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <button id="exportCustodyBtn" class="export-btn" style="display: flex; align-items: center; gap: 5px;">
            <i class="fas fa-file-export"></i>
            تصدير إلى Excel
          </button>
          <div class="items-per-page">
            <label for="custodyItemsPerPageSelect">عدد العناصر في الصفحة:</label>
            <select id="custodyItemsPerPageSelect">
              <option value="25">25</option>
              <option value="50" selected>50</option>
              <option value="100">100</option>
              <option value="200">200</option>
            </select>
          </div>
        </div>
        <table class="custody-table">
          <thead>
            <tr>
              <th>كود العهد</th>
              <th>اسم العهدة</th>
              <th>النوع</th>
              <th>الحالة</th>
              <th>العدد</th>
              <th>الكمية المصروفة</th>
              <th>الكمية المتبقية</th>
              <th>تاريخ الإضافة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="custodyTableBody"></tbody>
        </table>

        <!-- نظام الصفحات للعهد -->
        <div class="pagination-container" id="custodyPaginationContainer"></div>
      </div>
    </div>

    <!-- تسليم العهد -->
    <div class="tab-content" id="deliver-custody" style="display: none;">
      <div class="delivery-form">

        <form id="deliverCustodyForm" class="custody-form">
          <!-- إخفاء حقل رقم العملية من خانة الإدخال -->
          <input type="hidden" id="operationNumber">
          <input type="hidden" id="custodyTypeDeliver" readonly>
          <input type="hidden" id="custodyNameDeliver" readonly>
          <input type="hidden" id="deliveryStatus" value="مسلم">

          <!-- الصف الأول: كود الموظف، الإدارة، اسم الموظف، اسم العهدة -->
          <div class="form-row">
            <div class="form-group">
              <label for="employeeCodeDeliver">الكود أو الاسم:</label>
              <input type="text" id="employeeCodeDeliver" required placeholder="أدخل كود أو اسم الموظف">
              <div id="employee-delivery-suggestions" class="suggestions-list" style="display: none;"></div>
            </div>
            <div class="form-group">
              <label for="employeeDepartment">الإدارة:</label>
              <input type="text" id="employeeDepartment" readonly>
            </div>
            <div class="form-group">
              <label for="employeeName">اسم الموظف:</label>
              <input type="text" id="employeeName" readonly>
            </div>
            <div class="form-group">
              <label for="custodyCodeDeliver">إسم العهدة:</label>
              <div class="input-group custody-search-group">
                <input type="text" id="searchCustodyDeliver" placeholder="بحث عن العهدة..." class="search-input custody-search">
                <select id="custodyCodeDeliver" required class="custody-select">
                  <option value="">اختر العهدة</option>
                </select>
              </div>
            </div>
          </div>

          <!-- الصف الثاني: العدد، التاريخ -->
          <div class="form-row">
            <div class="form-group">
              <label for="deliveryQuantity">العدد:</label>
              <input type="number" id="deliveryQuantity" min="1" required>
            </div>
            <div class="form-group">
              <label for="deliveryDate">التاريخ:</label>
              <input type="date" id="deliveryDate" required>
            </div>
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
          </div>

          <!-- أزرار التحكم -->
          <div class="form-row">
            <div class="form-actions" style="grid-column: span 4;">
              <button type="submit" class="save-btn">حفظ التسليم</button>
              <button type="button" class="reset-btn">إعادة تعيين</button>
            </div>
          </div>
        </form>
      </div>

      <div class="delivery-table-container">
        <h3>سجل تسليم العهد</h3>
        <div class="search-filters">
          <input type="text" id="searchDelivery" placeholder="بحث باسم العهدة أو الموظف...">
          <button id="searchDeliveryBtn" class="search-btn">بحث</button>
        </div>
        <div class="table-controls" style="margin: 10px 0;">
          <button id="exportDeliveryBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <table class="delivery-table">
          <thead>
            <tr>
              <th>رقم العملية</th>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>كود العهد</th>
              <th>اسم العهدة</th>
              <th>نوع العهدة</th>
              <th>العدد</th>
              <th>التاريخ</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="deliveryTableBody"></tbody>
        </table>
      </div>
    </div>

    <!-- العهد غير المسلمة -->
    <div class="tab-content" id="undelivered-custody" style="display: none;">
      <div class="undelivered-container">
        <h2>العهد غير المسلمة</h2>
        <div class="undelivered-search" style="margin-bottom: 15px;">
          <input type="text" id="searchUndeliveredAll" placeholder="بحث بالكود أو العهدة أو النوع" style="width: 100%; padding: 10px; font-size: 16px; border-radius: 5px; border: 1px solid #ddd;">
        </div>
        <div class="table-controls" style="margin: 10px 0;">
          <button id="refreshUndeliveredBtn" class="refresh-btn">تحديث</button>
          <button id="exportUndeliveredBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <table class="undelivered-table">
          <thead>
            <tr>
              <th>كود العهد</th>
              <th>اسم العهدة</th>
              <th>النوع</th>
              <th>الحالة</th>
              <th>العدد المتاح</th>
              <th>تاريخ الإضافة</th>
            </tr>
          </thead>
          <tbody id="undeliveredTableBody"></tbody>
        </table>
      </div>
    </div>

    <!-- تقرير عهدة الموظف -->
    <div class="tab-content" id="employee-custody-report" style="display: none;">
      <div class="employee-custody-container">
        <h2>تقرير عهدة الموظف</h2>
        <div class="employee-search" style="margin-bottom: 15px;">
          <div class="form-group" style="flex: 1;">
            <label for="employeeReportSearch">بحث عن موظف:</label>
            <div class="input-group">
              <input type="text" id="employeeReportSearch" placeholder="أدخل كود أو اسم الموظف" style="width: 100%; padding: 10px; font-size: 16px; border-radius: 5px; border: 1px solid #ddd;">
              <button type="button" id="searchEmployeeReportBtn" class="search-btn">بحث</button>
            </div>
          </div>
        </div>
        <div class="table-controls">
          <button id="printEmployeeCustodyBtn" class="print-btn">طباعة</button>
          <button id="exportEmployeeCustodyBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <div class="employee-info-container" id="employeeInfoContainer" style="display: none; margin-bottom: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 5px; border: 1px solid #ddd;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <div>
              <strong>كود الموظف:</strong> <span id="reportEmployeeCode"></span>
            </div>
            <div>
              <strong>الاسم الكامل:</strong> <span id="reportEmployeeName"></span>
            </div>
            <div>
              <strong>الإدارة:</strong> <span id="reportEmployeeDepartment"></span>
            </div>
          </div>
        </div>
        <table class="report-table">
          <thead>
            <tr>
              <th>كود العهدة</th>
              <th>اسم العهدة</th>
              <th>نوع العهدة</th>
              <th>تاريخ الاستلام</th>
              <th>الكمية المستلمة</th>
            </tr>
          </thead>
          <tbody id="employeeCustodyTableBody"></tbody>
        </table>
      </div>
    </div>




  </div>

  <!-- نافذة تفاصيل المستلم -->
  <div id="custodyDetailsModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>تفاصيل مستلمي العهدة</h2>
      </div>
      <div class="modal-body">
        <div class="recipient-details">
          <div class="recipient-info">
            <div class="recipient-info-item">
              <span class="recipient-info-label">كود العهدة:</span>
              <span class="recipient-info-value" id="detailsCustodyCode"></span>
            </div>
            <div class="recipient-info-item">
              <span class="recipient-info-label">اسم العهدة:</span>
              <span class="recipient-info-value" id="detailsCustodyName"></span>
            </div>
            <div class="recipient-info-item">
              <span class="recipient-info-label">نوع العهدة:</span>
              <span class="recipient-info-value" id="detailsCustodyType"></span>
            </div>
          </div>
          <div class="recipient-balance">
            <div class="balance-item total-custody">
              <div class="balance-label">إجمالي العدد</div>
              <div class="balance-value" id="detailsTotalQuantity"></div>
            </div>
            <div class="balance-item delivered-custody">
              <div class="balance-label">المسلم</div>
              <div class="balance-value" id="detailsDeliveredQuantity"></div>
            </div>
            <div class="balance-item returned-custody">
              <div class="balance-label">المتاح</div>
              <div class="balance-value" id="detailsAvailableQuantity"></div>
            </div>
          </div>
        </div>
        <div class="custody-recipients-container">
          <h3>قائمة المستلمين</h3>
          <table class="custody-recipients-table">
            <thead>
              <tr>
                <th>كود الموظف</th>
                <th>اسم الموظف</th>
                <th>القسم</th>
                <th>الكمية</th>
                <th>تاريخ التسليم</th>
                <th>الحالة</th>
              </tr>
            </thead>
            <tbody id="custodyRecipientsTableBody"></tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button id="printCustodyDetailsBtn" class="print-btn">طباعة</button>
        <button id="exportCustodyDetailsBtn" class="export-btn">تصدير</button>
        <button class="close-btn">إغلاق</button>
      </div>
    </div>
  </div>

  <!-- Modal for editing custody -->
  <div id="editCustodyModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>تعديل العهدة</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <form id="editCustodyForm">
          <input type="hidden" id="editCustodyId">

          <!-- الصف الأول: اسم العهدة، نوع العهدة، الحالة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editCustodyName">اسم العهدة:</label>
              <input type="text" id="editCustodyName" required>
            </div>

            <div class="form-group">
              <label for="editCustodyType">نوع العهدة:</label>
              <select id="editCustodyType" required>
                <option value="لابتوب">لابتوب</option>
                <option value="موبايل">موبايل</option>
                <option value="شاشة">شاشة</option>
                <option value="طابعة">طابعة</option>
                <option value="كيبورد">كيبورد</option>
                <option value="ماوس">ماوس</option>
                <option value="أخرى">أخرى</option>
              </select>
            </div>

            <div class="form-group">
              <label for="editCustodyStatus">الحالة:</label>
              <select id="editCustodyStatus" required>
                  <option value="جديدة">جديدة</option>
                  <option value="مستعمل">مستعمل</option>
                  <option value="صيانة">صيانة</option>
                </select>
            </div>
          </div>

          <!-- الصف الثاني: العدد -->
          <div class="form-row">
            <div class="form-group">
              <label for="editCustodyQuantity">العدد:</label>
              <input type="number" id="editCustodyQuantity" min="1" required>
            </div>
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button id="saveEditBtn" class="confirm-btn">حفظ التغييرات</button>
        <button class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>

  <!-- Modal for editing delivery record -->
  <div id="editDeliveryModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>تعديل سجل تسليم العهد</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <form id="editDeliveryForm">
          <input type="hidden" id="editDeliveryId">
          <!-- الصف الأول: رقم العملية، كود الموظف، اسم الموظف -->
          <div class="form-row">
            <div class="form-group">
              <label for="editDeliveryOperationNumber">رقم العملية:</label>
              <input type="text" id="editDeliveryOperationNumber" readonly>
            </div>
            <div class="form-group">
              <label for="editDeliveryEmployeeCode">كود الموظف:</label>
              <input type="text" id="editDeliveryEmployeeCode" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
              <label for="editDeliveryEmployeeName">اسم الموظف:</label>
              <input type="text" id="editDeliveryEmployeeName" readonly style="background-color: #f5f5f5;">
            </div>
          </div>

          <!-- الصف الثاني: القسم، كود العهدة، اسم العهدة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editDeliveryDepartment">القسم:</label>
              <input type="text" id="editDeliveryDepartment" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
              <label for="editDeliveryCustodyCode">كود العهدة:</label>
              <input type="text" id="editDeliveryCustodyCode" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
              <label for="editDeliveryCustodyName">اسم العهدة:</label>
              <input type="text" id="editDeliveryCustodyName" readonly style="background-color: #f5f5f5;">
            </div>
          </div>

          <!-- الصف الثالث: نوع العهدة، الكمية، تاريخ التسليم -->
          <div class="form-row">
            <div class="form-group">
              <label for="editDeliveryCustodyType">نوع العهدة:</label>
              <input type="text" id="editDeliveryCustodyType" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
              <label for="editDeliveryQuantity">الكمية:</label>
              <input type="number" id="editDeliveryQuantity" min="1" required>
            </div>
            <div class="form-group">
              <label for="editDeliveryDate">تاريخ التسليم:</label>
              <input type="date" id="editDeliveryDate" required>
            </div>
          </div>

          <!-- الصف الرابع: الحالة (مخفي - افتراضي مسلم) -->
          <input type="hidden" id="editDeliveryStatus" value="مسلم">
        </form>
      </div>
      <div class="modal-footer">
        <button id="saveEditDeliveryBtn" class="confirm-btn">حفظ التغييرات</button>
        <button class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>



  <script src="dateUtils.js"></script>
  <script src="form-validation.js"></script>
  <script src="universal-pagination-integration.js"></script>
  <script src="custody.js"></script>

  

<script>
// دالة تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح البيانات المحفوظة
        localStorage.removeItem('token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('activeSection');
        localStorage.removeItem('userName');
        
        // إعادة التوجيه لصفحة تسجيل الدخول
        window.location.href = 'login.html';
    }
}

// تم حذف دالة updateSidebarUserInfo المتعارضة - يتم التحكم بها من sidebar-standalone.js فقط

// تحميل القائمة الجانبية مع النظام المحسن
</script>
</body>
</html>