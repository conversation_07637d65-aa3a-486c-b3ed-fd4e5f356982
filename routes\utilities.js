const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  const jwt = require('jsonwebtoken');
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// إنشاء جدول التقييمات إذا لم يكن موجودًا
const createEvaluationsTable = async (pool) => {
  const sql = `CREATE TABLE IF NOT EXISTS evaluations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_code VARCHAR(50) NOT NULL,
    employee_name VARCHAR(255) NOT NULL,
    department VARCHAR(255) NOT NULL,
    evaluation_type ENUM('شهري', 'ربع سنوي') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
  )`;
  await pool.promise().query(sql);
};

// إنشاء جداول الصور والمستندات
const createEmployeeFileTables = async (pool) => {
  // جدول صور الموظفين
  const photoTableSql = `CREATE TABLE IF NOT EXISTS employee_photos (
    id int NOT NULL AUTO_INCREMENT,
    employee_code varchar(50) NOT NULL COMMENT 'كود الموظف',
    photo_path varchar(500) NOT NULL COMMENT 'مسار الصورة',
    original_name varchar(255) NOT NULL COMMENT 'الاسم الأصلي للملف',
    file_size int NOT NULL COMMENT 'حجم الملف بالبايت',
    mime_type varchar(100) NOT NULL COMMENT 'نوع الملف',
    uploaded_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الرفع',
    PRIMARY KEY (id),
    UNIQUE KEY unique_employee_photo (employee_code),
    KEY idx_employee_code (employee_code)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول صور الموظفين'`;

  // جدول مستندات الموظفين
  const documentTableSql = `CREATE TABLE IF NOT EXISTS employee_documents (
    id int NOT NULL AUTO_INCREMENT,
    employee_code varchar(50) NOT NULL COMMENT 'كود الموظف',
    document_path varchar(500) NOT NULL COMMENT 'مسار المستند',
    original_name varchar(255) NOT NULL COMMENT 'الاسم الأصلي للملف',
    file_size int NOT NULL COMMENT 'حجم الملف بالبايت',
    mime_type varchar(100) NOT NULL COMMENT 'نوع الملف',
    document_type varchar(100) DEFAULT NULL COMMENT 'نوع المستند',
    uploaded_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الرفع',
    PRIMARY KEY (id),
    KEY idx_employee_code (employee_code),
    KEY idx_document_type (document_type)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول مستندات الموظفين'`;

  try {
    await pool.promise().query(photoTableSql);
    await pool.promise().query(documentTableSql);
    console.log('تم التحقق من جداول الصور والمستندات');
  } catch (error) {
    console.error('خطأ في إنشاء جداول الصور والمستندات:', error);
    throw error;
  }
};

// إنشاء جدول سجل الأنشطة إذا لم يكن موجودًا
const createActivityLogsTable = async (pool) => {
  const sql = `CREATE TABLE IF NOT EXISTS activity_logs (
    id int NOT NULL AUTO_INCREMENT,
    user_id int DEFAULT NULL COMMENT 'معرف المستخدم',
    username varchar(50) NOT NULL COMMENT 'اسم المستخدم',
    action_type varchar(50) NOT NULL COMMENT 'نوع العملية',
    module varchar(50) NOT NULL COMMENT 'القسم',
    record_id varchar(50) DEFAULT NULL COMMENT 'معرف السجل المتأثر',
    message text NOT NULL COMMENT 'وصف الإجراء',
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ التنفيذ',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_username (username),
    KEY idx_action_type (action_type),
    KEY idx_module (module),
    KEY idx_record_id (record_id),
    KEY idx_created_at (created_at),
    KEY idx_action_module (action_type, module)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سجل الأنشطة'`;

  try {
    await pool.promise().query(sql);

    // إضافة بعض البيانات التجريبية إذا كان الجدول فارغ
    const [rows] = await pool.promise().query('SELECT COUNT(*) as count FROM activity_logs');
    if (rows[0].count === 0) {
      const sampleData = [
        [null, 'admin', 'login', 'system', null, 'تسجيل دخول المستخدم إلى النظام'],
        [null, 'admin', 'add', 'employees', '12345', 'تم إضافة موظف جديد: أحمد محمد علي (كود: 12345)'],
        [null, 'admin', 'edit', 'employees', '12345', 'تم تعديل بيانات الموظف: أحمد محمد علي (كود: 12345)'],
        [null, 'admin', 'print', 'vacations', null, 'تم طباعة تقرير الإجازات'],
        [null, 'admin', 'deliver', 'custody', 'OP001', 'تم تسليم عهدة: لابتوب ديل للموظف: أحمد محمد علي'],
        [null, 'admin', 'return', 'custody', 'OP001', 'تم استرجاع عهدة: لابتوب ديل من الموظف: أحمد محمد علي'],
        [null, 'admin', 'delete', 'employees', '99999', 'تم حذف الموظف: موظف تجريبي (كود: 99999)'],
        [null, 'admin', 'logout', 'system', null, 'تسجيل خروج المستخدم من النظام']
      ];

      for (const data of sampleData) {
        await pool.promise().query(
          'INSERT INTO activity_logs (user_id, username, action_type, module, record_id, message) VALUES (?, ?, ?, ?, ?, ?)',
          data
        );
      }
    }
  } catch (error) {
    console.error('خطأ في إنشاء جدول سجل الأنشطة:', error);
  }
};

// إنشاء جدول المستخدمين إذا لم يكن موجودًا
const createUsersTable = async (pool) => {
  try {
    // التحقق من وجود جدول المستخدمين
    const [tables] = await pool.promise().query(
      "SHOW TABLES LIKE 'users'"
    );
    
    if (tables.length === 0) {
      // إنشاء جدول المستخدمين
      await pool.promise().query(`
        CREATE TABLE users (
          id int NOT NULL AUTO_INCREMENT,
          username varchar(50) NOT NULL,
          password varchar(255) NOT NULL,
          employee_code int DEFAULT NULL COMMENT 'كود الموظف المرتبط بالمستخدم',
          permissions JSON DEFAULT NULL,
          created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY username (username),
          UNIQUE KEY employee_code (employee_code),
          KEY idx_employee_code (employee_code)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
      `);
      
      // إنشاء مستخدم admin افتراضي
      const hashedPassword = await bcrypt.hash('123456', 10);
      await pool.promise().query(
        "INSERT INTO users (username, password, permissions) VALUES (?, ?, ?)",
        [
          'admin', 
          hashedPassword, 
          JSON.stringify({
            can_view: true,
            can_add: true,
            can_edit: true,
            can_delete: true,
            view_employees: true,
            view_vacations: true,
            add_vacation: true,
            view_vacations_list: true,
            view_vacation_reports: true,
            view_contributions: true,
            view_rewards_list: true,
            add_reward: true,
            export_rewards: true,
            view_deductions_list: true,
            add_deduction: true,
            export_deductions: true,
            view_resignations: true,
            add_resignation: true,
            edit_resignation: true,
            delete_resignation: true,
            view_training: true,
            add_training: true,
            edit_training: true,
            delete_training: true,
            view_salary_advances: true,
            add_salary_advance: true,
            edit_salary_advance: true,
            delete_salary_advance: true,
            view_extra_hours: true,
            add_extra_hours: true,
            edit_extra_hours: true,
            delete_extra_hours: true,
            view_activity_logs: true
          })
        ]
      );
      
      console.log('تم إنشاء جدول المستخدمين ومستخدم admin افتراضي');
    }
  } catch (error) {
    console.error('خطأ في إنشاء جدول المستخدمين:', error);
    throw error;
  }
};

// Endpoint: إنشاء جدول التقييمات
router.get('/create-evaluations-table', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createEvaluationsTable(pool);
    res.json({ message: 'تم التحقق من جدول التقييمات' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول التقييمات:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول التقييمات' });
  }
});

// Endpoint: إنشاء جداول الصور والمستندات
router.get('/create-employee-file-tables', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createEmployeeFileTables(pool);
    res.json({ message: 'تم التحقق من جداول الصور والمستندات' });
  } catch (error) {
    console.error('خطأ في إنشاء جداول الصور والمستندات:', error);
    res.status(500).json({ error: 'فشل في إنشاء جداول الصور والمستندات' });
  }
});

// Endpoint: إنشاء جدول سجل الأنشطة
router.get('/create-activity-logs-table', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createActivityLogsTable(pool);
    res.json({ message: 'تم التحقق من جدول سجل الأنشطة' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول سجل الأنشطة:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول سجل الأنشطة' });
  }
});

// Endpoint: مسح جميع البيانات التجريبية من سجل الأنشطة
router.delete('/clear-activity-logs', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const [result] = await pool.promise().query('DELETE FROM activity_logs');
    res.json({
      message: 'تم مسح جميع البيانات من سجل الأنشطة',
      deletedRows: result.affectedRows
    });
  } catch (error) {
    console.error('خطأ في مسح سجل الأنشطة:', error);
    res.status(500).json({ error: 'فشل في مسح سجل الأنشطة' });
  }
});

// Endpoint: إعادة إنشاء جدول سجل الأنشطة
router.get('/recreate-activity-logs-table', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    // حذف الجدول إذا كان موجود
    await pool.promise().query('DROP TABLE IF EXISTS activity_logs');
    
    // إعادة إنشاء الجدول
    await createActivityLogsTable(pool);
    
    res.json({ message: 'تم إعادة إنشاء جدول سجل الأنشطة بنجاح' });
  } catch (error) {
    console.error('خطأ في إعادة إنشاء جدول سجل الأنشطة:', error);
    res.status(500).json({ error: 'فشل في إعادة إنشاء جدول سجل الأنشطة' });
  }
});

// التحقق من حالة الخادم
router.get('/status', (req, res) => {
  res.json({ status: 'online' });
});

module.exports = router;
