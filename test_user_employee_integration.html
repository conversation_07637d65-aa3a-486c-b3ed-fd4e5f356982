<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ربط المستخدمين بالموظفين</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f8f9fa;
        }
        .json-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار ربط المستخدمين بالموظفين</h1>
        
        <div class="section">
            <h2>1. اختبار الاتصال بالخادم</h2>
            <button onclick="testServerConnection()">اختبار الاتصال</button>
            <div id="connectionResult"></div>
        </div>

        <div class="section">
            <h2>2. عرض المستخدمين مع بيانات الموظفين</h2>
            <button onclick="loadUsers()">تحميل المستخدمين</button>
            <div id="usersResult"></div>
        </div>

        <div class="section">
            <h2>3. عرض الموظفين المتاحين</h2>
            <button onclick="loadAvailableEmployees()">تحميل الموظفين المتاحين</button>
            <div id="availableEmployeesResult"></div>
        </div>

        <div class="section">
            <h2>4. اختبار إضافة مستخدم مرتبط بموظف</h2>
            <button onclick="testAddUserWithEmployee()">إضافة مستخدم تجريبي</button>
            <div id="addUserResult"></div>
        </div>

        <div class="section">
            <h2>5. اختبار تحديث ربط المستخدم</h2>
            <button onclick="testUpdateUserEmployee()">تحديث ربط المستخدم</button>
            <div id="updateUserResult"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:5500/api';
        let token = localStorage.getItem('token');

        // إذا لم يكن هناك توكن، استخدم توكن تجريبي (يجب تسجيل الدخول أولاً)
        if (!token) {
            alert('يرجى تسجيل الدخول أولاً للحصول على التوكن');
        }

        async function testServerConnection() {
            const resultDiv = document.getElementById('connectionResult');
            try {
                const response = await fetch(`${API_URL}/status`);
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="test-result success">✅ الخادم متصل ويعمل بشكل طبيعي</div>';
                } else {
                    resultDiv.innerHTML = '<div class="test-result error">❌ الخادم غير متاح</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ خطأ في الاتصال: ${error.message}</div>`;
            }
        }

        async function loadUsers() {
            const resultDiv = document.getElementById('usersResult');
            try {
                const response = await fetch(`${API_URL}/users`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const users = await response.json();
                    let html = '<div class="test-result success">✅ تم تحميل المستخدمين بنجاح</div>';
                    
                    if (users.length > 0) {
                        html += '<table><thead><tr><th>ID</th><th>اسم المستخدم</th><th>كود الموظف</th><th>اسم الموظف</th><th>القسم</th><th>المنصب</th></tr></thead><tbody>';
                        users.forEach(user => {
                            html += `<tr>
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${user.employee_code || 'غير مرتبط'}</td>
                                <td>${user.employee_name || 'غير مرتبط'}</td>
                                <td>${user.department || '-'}</td>
                                <td>${user.job_title || '-'}</td>
                            </tr>`;
                        });
                        html += '</tbody></table>';
                    } else {
                        html += '<div class="test-result info">ℹ️ لا يوجد مستخدمين</div>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `<div class="test-result error">❌ فشل في تحميل المستخدمين: ${error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ خطأ: ${error.message}</div>`;
            }
        }

        async function loadAvailableEmployees() {
            const resultDiv = document.getElementById('availableEmployeesResult');
            try {
                const response = await fetch(`${API_URL}/available-employees`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const employees = await response.json();
                    let html = '<div class="test-result success">✅ تم تحميل الموظفين المتاحين بنجاح</div>';
                    
                    if (employees.length > 0) {
                        html += '<table><thead><tr><th>كود الموظف</th><th>الاسم</th><th>القسم</th><th>المنصب</th></tr></thead><tbody>';
                        employees.forEach(employee => {
                            html += `<tr>
                                <td>${employee.code}</td>
                                <td>${employee.full_name}</td>
                                <td>${employee.department}</td>
                                <td>${employee.job_title}</td>
                            </tr>`;
                        });
                        html += '</tbody></table>';
                    } else {
                        html += '<div class="test-result info">ℹ️ جميع الموظفين مرتبطين بمستخدمين أو لا يوجد موظفين نشطين</div>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    const error = await response.text();
                    resultDiv.innerHTML = `<div class="test-result error">❌ فشل في تحميل الموظفين المتاحين: ${error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ خطأ: ${error.message}</div>`;
            }
        }

        async function testAddUserWithEmployee() {
            const resultDiv = document.getElementById('addUserResult');
            
            // أولاً، احصل على قائمة الموظفين المتاحين
            try {
                const employeesResponse = await fetch(`${API_URL}/available-employees`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!employeesResponse.ok) {
                    resultDiv.innerHTML = '<div class="test-result error">❌ لا يمكن الحصول على قائمة الموظفين المتاحين</div>';
                    return;
                }

                const availableEmployees = await employeesResponse.json();
                
                if (availableEmployees.length === 0) {
                    resultDiv.innerHTML = '<div class="test-result info">ℹ️ لا يوجد موظفين متاحين للربط</div>';
                    return;
                }

                // استخدم أول موظف متاح
                const firstEmployee = availableEmployees[0];
                
                const testUser = {
                    username: `test_user_${Date.now()}`,
                    password: '123456',
                    employee_code: firstEmployee.code,
                    permissions: {
                        can_view: true,
                        view_employees: true
                    }
                };

                const response = await fetch(`${API_URL}/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(testUser)
                });

                if (response.ok) {
                    const result = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">✅ تم إنشاء المستخدم بنجاح</div>
                        <div class="json-display">${JSON.stringify(result, null, 2)}</div>
                        <div class="test-result info">ℹ️ المستخدم مرتبط بالموظف: ${firstEmployee.full_name} (${firstEmployee.code})</div>
                    `;
                } else {
                    const error = await response.json();
                    resultDiv.innerHTML = `<div class="test-result error">❌ فشل في إنشاء المستخدم: ${error.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">❌ خطأ: ${error.message}</div>`;
            }
        }

        async function testUpdateUserEmployee() {
            const resultDiv = document.getElementById('updateUserResult');
            resultDiv.innerHTML = '<div class="test-result info">ℹ️ هذه الوظيفة تحتاج إلى تنفيذ يدوي حسب المستخدمين الموجودين</div>';
        }

        // تحميل التوكن عند تحميل الصفحة
        window.addEventListener('load', () => {
            if (token) {
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #d4edda; padding: 10px; margin-bottom: 20px; border-radius: 4px;">✅ تم العثور على التوكن - يمكنك الآن اختبار الوظائف</div>'
                );
            } else {
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #f8d7da; padding: 10px; margin-bottom: 20px; border-radius: 4px;">⚠️ لم يتم العثور على التوكن - يرجى تسجيل الدخول أولاً</div>'
                );
            }
        });
    </script>
</body>
</html>
