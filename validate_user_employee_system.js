/**
 * ملف التحقق من سلامة نظام ربط المستخدمين بالموظفين
 * يتحقق من جميع جوانب النظام ويقدم تقرير مفصل
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// إعدادات قاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'employee_system',
  charset: 'utf8mb4'
};

class SystemValidator {
  constructor() {
    this.connection = null;
    this.results = {
      database: { passed: 0, failed: 0, warnings: 0 },
      structure: { passed: 0, failed: 0, warnings: 0 },
      data: { passed: 0, failed: 0, warnings: 0 },
      integrity: { passed: 0, failed: 0, warnings: 0 }
    };
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(dbConfig);
      console.log('✅ تم الاتصال بقاعدة البيانات');
      return true;
    } catch (error) {
      console.error('❌ فشل الاتصال بقاعدة البيانات:', error.message);
      return false;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log('🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }

  log(type, message, category = 'general') {
    const icons = { pass: '✅', fail: '❌', warn: '⚠️', info: 'ℹ️' };
    console.log(`${icons[type]} ${message}`);
    
    if (type === 'pass') this.results[category].passed++;
    else if (type === 'fail') this.results[category].failed++;
    else if (type === 'warn') this.results[category].warnings++;
  }

  async validateDatabaseStructure() {
    console.log('\n🏗️ التحقق من هيكل قاعدة البيانات...');

    // التحقق من وجود جدول المستخدمين
    const [usersTables] = await this.connection.execute("SHOW TABLES LIKE 'users'");
    if (usersTables.length > 0) {
      this.log('pass', 'جدول المستخدمين موجود', 'structure');
    } else {
      this.log('fail', 'جدول المستخدمين غير موجود', 'structure');
      return false;
    }

    // التحقق من وجود جدول الموظفين
    const [employeesTables] = await this.connection.execute("SHOW TABLES LIKE 'employees'");
    if (employeesTables.length > 0) {
      this.log('pass', 'جدول الموظفين موجود', 'structure');
    } else {
      this.log('fail', 'جدول الموظفين غير موجود', 'structure');
      return false;
    }

    // التحقق من الحقول المطلوبة في جدول المستخدمين
    const [usersColumns] = await this.connection.execute("DESCRIBE users");
    const requiredColumns = ['id', 'username', 'password', 'employee_code', 'permissions', 'created_at'];
    const existingColumns = usersColumns.map(col => col.Field);

    requiredColumns.forEach(col => {
      if (existingColumns.includes(col)) {
        this.log('pass', `حقل ${col} موجود في جدول المستخدمين`, 'structure');
      } else {
        this.log('fail', `حقل ${col} غير موجود في جدول المستخدمين`, 'structure');
      }
    });

    return true;
  }

  async validateIndexesAndConstraints() {
    console.log('\n🔗 التحقق من الفهارس والمفاتيح الخارجية...');

    // التحقق من الفهرس الفريد على employee_code
    const [uniqueIndex] = await this.connection.execute(
      "SHOW INDEX FROM users WHERE Key_name = 'uk_employee_code'"
    );
    if (uniqueIndex.length > 0) {
      this.log('pass', 'الفهرس الفريد على employee_code موجود', 'structure');
    } else {
      this.log('warn', 'الفهرس الفريد على employee_code غير موجود', 'structure');
    }

    // التحقق من الفهرس العادي على employee_code
    const [normalIndex] = await this.connection.execute(
      "SHOW INDEX FROM users WHERE Key_name = 'idx_employee_code'"
    );
    if (normalIndex.length > 0) {
      this.log('pass', 'الفهرس على employee_code موجود', 'structure');
    } else {
      this.log('warn', 'الفهرس على employee_code غير موجود', 'structure');
    }

    // التحقق من المفتاح الخارجي
    const [foreignKey] = await this.connection.execute(
      "SELECT CONSTRAINT_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE CONSTRAINT_SCHEMA = ? AND TABLE_NAME = 'users' AND CONSTRAINT_NAME = 'fk_users_employee'",
      [dbConfig.database]
    );
    if (foreignKey.length > 0) {
      this.log('pass', 'المفتاح الخارجي fk_users_employee موجود', 'structure');
    } else {
      this.log('warn', 'المفتاح الخارجي fk_users_employee غير موجود', 'structure');
    }
  }

  async validateDataIntegrity() {
    console.log('\n🔍 التحقق من سلامة البيانات...');

    // التحقق من المستخدمين المرتبطين بموظفين غير موجودين
    const [orphanedUsers] = await this.connection.execute(`
      SELECT u.id, u.username, u.employee_code
      FROM users u
      LEFT JOIN employees e ON u.employee_code = e.code
      WHERE u.employee_code IS NOT NULL AND e.code IS NULL
    `);

    if (orphanedUsers.length === 0) {
      this.log('pass', 'جميع المستخدمين المرتبطين مرتبطين بموظفين موجودين', 'integrity');
    } else {
      this.log('fail', `${orphanedUsers.length} مستخدمين مرتبطين بموظفين غير موجودين`, 'integrity');
      orphanedUsers.forEach(user => {
        console.log(`  - المستخدم ${user.username} (ID: ${user.id}) مرتبط بالموظف ${user.employee_code} غير الموجود`);
      });
    }

    // التحقق من تكرار ربط الموظفين
    const [duplicateEmployees] = await this.connection.execute(`
      SELECT employee_code, COUNT(*) as count
      FROM users
      WHERE employee_code IS NOT NULL
      GROUP BY employee_code
      HAVING COUNT(*) > 1
    `);

    if (duplicateEmployees.length === 0) {
      this.log('pass', 'لا يوجد موظفين مرتبطين بأكثر من مستخدم', 'integrity');
    } else {
      this.log('fail', `${duplicateEmployees.length} موظفين مرتبطين بأكثر من مستخدم`, 'integrity');
      duplicateEmployees.forEach(emp => {
        console.log(`  - الموظف ${emp.employee_code} مرتبط بـ ${emp.count} مستخدمين`);
      });
    }

    // التحقق من صلاحيات المستخدم admin
    const [adminUser] = await this.connection.execute(
      "SELECT permissions FROM users WHERE username = 'admin'"
    );

    if (adminUser.length > 0) {
      const permissions = JSON.parse(adminUser[0].permissions || '{}');
      const requiredPermissions = ['manage_users', 'can_view', 'can_add', 'can_edit', 'can_delete'];
      const missingPermissions = requiredPermissions.filter(perm => !permissions[perm]);

      if (missingPermissions.length === 0) {
        this.log('pass', 'المستخدم admin لديه جميع الصلاحيات المطلوبة', 'data');
      } else {
        this.log('warn', `المستخدم admin يفتقد الصلاحيات: ${missingPermissions.join(', ')}`, 'data');
      }
    } else {
      this.log('fail', 'المستخدم admin غير موجود', 'data');
    }
  }

  async generateStatistics() {
    console.log('\n📊 إحصائيات النظام...');

    // إحصائيات المستخدمين
    const [totalUsers] = await this.connection.execute("SELECT COUNT(*) as count FROM users");
    const [linkedUsers] = await this.connection.execute("SELECT COUNT(*) as count FROM users WHERE employee_code IS NOT NULL");
    const [unlinkedUsers] = await this.connection.execute("SELECT COUNT(*) as count FROM users WHERE employee_code IS NULL");

    console.log(`📈 إجمالي المستخدمين: ${totalUsers[0].count}`);
    console.log(`🔗 المستخدمين المرتبطين: ${linkedUsers[0].count}`);
    console.log(`🔓 المستخدمين غير المرتبطين: ${unlinkedUsers[0].count}`);

    // إحصائيات الموظفين
    const [totalEmployees] = await this.connection.execute("SELECT COUNT(*) as count FROM employees WHERE status = 'نشط'");
    const [availableEmployees] = await this.connection.execute(`
      SELECT COUNT(*) as count 
      FROM employees e
      LEFT JOIN users u ON e.code = u.employee_code
      WHERE u.employee_code IS NULL AND e.status = 'نشط'
    `);

    console.log(`👥 إجمالي الموظفين النشطين: ${totalEmployees[0].count}`);
    console.log(`🆓 الموظفين المتاحين للربط: ${availableEmployees[0].count}`);

    // نسبة الربط
    const linkagePercentage = totalUsers[0].count > 0 ? 
      ((linkedUsers[0].count / totalUsers[0].count) * 100).toFixed(1) : 0;
    console.log(`📊 نسبة الربط: ${linkagePercentage}%`);
  }

  async generateReport() {
    console.log('\n📋 تقرير التحقق النهائي...');
    
    const total = Object.values(this.results).reduce((acc, cat) => ({
      passed: acc.passed + cat.passed,
      failed: acc.failed + cat.failed,
      warnings: acc.warnings + cat.warnings
    }), { passed: 0, failed: 0, warnings: 0 });

    console.log(`✅ اختبارات نجحت: ${total.passed}`);
    console.log(`❌ اختبارات فشلت: ${total.failed}`);
    console.log(`⚠️ تحذيرات: ${total.warnings}`);

    const overallStatus = total.failed === 0 ? 
      (total.warnings === 0 ? 'ممتاز' : 'جيد مع تحذيرات') : 'يحتاج إصلاح';
    
    console.log(`\n🎯 الحالة العامة: ${overallStatus}`);

    if (total.failed > 0) {
      console.log('\n🔧 إجراءات مطلوبة:');
      console.log('1. تشغيل ملف update_users_employee_link.sql');
      console.log('2. تشغيل ملف migration_user_employee_link.js');
      console.log('3. إعادة تشغيل هذا التحقق');
    }

    return total.failed === 0;
  }

  async validate() {
    const connected = await this.connect();
    if (!connected) return false;

    try {
      await this.validateDatabaseStructure();
      await this.validateIndexesAndConstraints();
      await this.validateDataIntegrity();
      await this.generateStatistics();
      return await this.generateReport();
    } catch (error) {
      console.error('❌ خطأ أثناء التحقق:', error.message);
      return false;
    } finally {
      await this.disconnect();
    }
  }
}

// تشغيل التحقق إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  const validator = new SystemValidator();
  validator.validate().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = SystemValidator;
