/**
 * تطبيق نظام الصفحات الموحد على جميع الجداول
 * Universal Pagination Integration for All Tables
 */

// كائن لتخزين جميع أنظمة الصفحات
window.PaginationInstances = {};

/**
 * إنشاء نظام صفحات لجدول معين
 * @param {string} tableId - معرف الجدول
 * @param {Object} options - خيارات النظام
 */
function createTablePagination(tableId, options = {}) {
  const tableBody = document.querySelector(`#${tableId} tbody`);
  const paginationContainer = document.getElementById(options.paginationContainerId);
  const itemsPerPageSelect = document.getElementById(options.itemsPerPageSelectId);
  
  if (!tableBody) {
    console.warn(`Table body not found for table: ${tableId}`);
    return null;
  }

  const paginationSystem = new PaginationSystem({
    tableBody: tableBody,
    paginationContainer: paginationContainer,
    itemsPerPageSelect: itemsPerPageSelect,
    renderRowFunction: options.renderRowFunction,
    noDataMessage: options.noDataMessage || 'لا توجد بيانات للعرض',
    columnsCount: options.columnsCount || 6,
    itemsPerPage: options.itemsPerPage || 50
  });

  // حفظ المثيل للاستخدام لاحقاً
  window.PaginationInstances[tableId] = paginationSystem;
  
  return paginationSystem;
}

/**
 * دالة عرض صف الموظفين (للجدول الرئيسي)
 */
function renderEmployeeRow(emp) {
  const displayValue = (value) => value === null ? '-' : value;
  
  // إنشاء أزرار الإجراءات بناءً على الصلاحيات
  let actionsHTML = '';
  
  // زر العرض متاح دائماً
  actionsHTML += `<button class="view-btn" data-id="${emp.code}">عرض</button>`;
  
  // زر التعديل حسب الصلاحية
  if (window.hasPermission && window.hasPermission('can_edit')) {
    actionsHTML += `<button class="edit-btn" data-id="${emp.code}">تعديل</button>`;
  }
  
  // زر الحذف حسب الصلاحية
  if (window.hasPermission && window.hasPermission('can_delete')) {
    actionsHTML += `<button class="delete-btn" data-id="${emp.code}">حذف</button>`;
  }
  
  return `<td>${displayValue(emp.code)}</td>
<td>${displayValue(emp.full_name)}</td>
<td>${displayValue(emp.department)}</td>
<td>${displayValue(emp.job_title)}</td>
<td>${emp.hire_date ? formatDate(emp.hire_date) : '-'}</td>
<td>
  ${actionsHTML}
</td>`;
}

/**
 * دالة عرض صف التدريب
 */
function renderTrainingRow(training) {
  const displayValue = (value) => value === null ? '-' : value;
  
  return `<td>${displayValue(training.employee_code)}</td>
<td>${displayValue(training.employee_name)}</td>
<td>${displayValue(training.department)}</td>
<td>${displayValue(training.course_name)}</td>
<td>${training.course_date ? formatDate(training.course_date) : '-'}</td>
<td>${displayValue(training.duration)}</td>
<td>${displayValue(training.training_type)}</td>
<td>${displayValue(training.cost)}</td>
<td>${displayValue(training.notes)}</td>
<td>
  <button class="edit-btn" data-id="${training.id}">تعديل</button>
  <button class="delete-btn" data-id="${training.id}">حذف</button>
</td>`;
}

/**
 * دالة عرض صف التقييم
 */
function renderEvaluationRow(evaluation) {
  const displayValue = (value) => value === null ? '-' : value;
  
  return `<td>${displayValue(evaluation.employee_code)}</td>
<td>${displayValue(evaluation.employee_name)}</td>
<td>${displayValue(evaluation.department)}</td>
<td>${displayValue(evaluation.evaluation_type)}</td>
<td>${evaluation.start_date ? formatDate(evaluation.start_date) : '-'}</td>
<td>${evaluation.end_date ? formatDate(evaluation.end_date) : '-'}</td>
<td>${displayValue(evaluation.score)}</td>
<td>${displayValue(evaluation.notes)}</td>
<td>
  <button class="edit-btn" data-id="${evaluation.id}">تعديل</button>
  <button class="delete-btn" data-id="${evaluation.id}">حذف</button>
</td>`;
}

/**
 * دالة عرض صف الإجازات
 */
function renderVacationRow(vacation) {
  const displayValue = (value) => value === null ? '-' : value;
  
  return `<td>${displayValue(vacation.employee_code)}</td>
<td>${displayValue(vacation.employee_name)}</td>
<td>${displayValue(vacation.casual_leave)}</td>
<td>${displayValue(vacation.authorized_absence)}</td>
<td>${displayValue(vacation.unauthorized_absence)}</td>
<td>${displayValue(vacation.annual_leave)}</td>
<td>${displayValue(vacation.unpaid_leave)}</td>
<td>${displayValue(vacation.sick_leave)}</td>
<td>${displayValue(vacation.extra_leave)}</td>
<td>
  <button class="details-btn" data-id="${vacation.employee_code}">التفاصيل</button>
</td>`;
}

/**
 * دالة عرض صف العهد
 */
function renderCustodyRow(custody) {
  const displayValue = (value) => value === null ? '-' : value;
  
  return `<td>${displayValue(custody.custody_code)}</td>
<td>${displayValue(custody.custody_name)}</td>
<td>${displayValue(custody.custody_type)}</td>
<td>${displayValue(custody.status)}</td>
<td>${displayValue(custody.quantity)}</td>
<td>${displayValue(custody.delivered_quantity)}</td>
<td>${displayValue(custody.remaining_quantity)}</td>
<td>${custody.created_at ? formatDate(custody.created_at) : '-'}</td>
<td>
  <button class="edit-btn" data-id="${custody.id}">تعديل</button>
  <button class="delete-btn" data-id="${custody.id}">حذف</button>
</td>`;
}

/**
 * تهيئة نظام الصفحات للجدول الرئيسي (الموظفين)
 */
function initializeMainTablePagination() {
  if (document.getElementById('employeeTableBody')) {
    createTablePagination('employee-table', {
      paginationContainerId: 'paginationContainer',
      itemsPerPageSelectId: 'itemsPerPageSelect',
      renderRowFunction: renderEmployeeRow,
      noDataMessage: 'لا توجد بيانات موظفين للعرض',
      columnsCount: 6
    });
  }
}

/**
 * تهيئة نظام الصفحات لجدول التدريب
 */
function initializeTrainingTablePagination() {
  if (document.getElementById('training-table')) {
    createTablePagination('training-table', {
      paginationContainerId: 'trainingPaginationContainer',
      itemsPerPageSelectId: 'trainingItemsPerPageSelect',
      renderRowFunction: renderTrainingRow,
      noDataMessage: 'لا توجد بيانات تدريب للعرض',
      columnsCount: 10
    });
  }

  // جدول تقارير التدريب
  if (document.getElementById('report-table')) {
    createTablePagination('report-table', {
      paginationContainerId: 'trainingReportPaginationContainer',
      itemsPerPageSelectId: 'trainingReportItemsPerPageSelect',
      renderRowFunction: (item) => renderTrainingRow(item).replace(/<td>.*?<\/td>$/, ''), // إزالة عمود الإجراءات
      noDataMessage: 'لا توجد بيانات في التقرير',
      columnsCount: 8
    });
  }
}

/**
 * تهيئة نظام الصفحات لجدول التقييمات
 */
function initializeEvaluationTablePagination() {
  if (document.getElementById('evaluation-table')) {
    createTablePagination('evaluation-table', {
      paginationContainerId: 'evaluationPaginationContainer',
      itemsPerPageSelectId: 'evaluationItemsPerPageSelect',
      renderRowFunction: renderEvaluationRow,
      noDataMessage: 'لا توجد بيانات تقييم للعرض',
      columnsCount: 9
    });
  }

  // جدول الموظفين غير المقيمين
  if (document.getElementById('unevaluated-employees-table')) {
    createTablePagination('unevaluated-employees-table', {
      paginationContainerId: 'unevaluatedPaginationContainer',
      itemsPerPageSelectId: 'unevaluatedItemsPerPageSelect',
      renderRowFunction: (emp) => `<td>${emp.code}</td>
<td>${emp.full_name}</td>
<td>${emp.department}</td>
<td>${emp.job_title}</td>
<td>${emp.hire_date ? formatDate(emp.hire_date) : '-'}</td>
<td>${emp.last_evaluation || '-'}</td>
<td>${emp.days_since_evaluation || '-'}</td>`,
      noDataMessage: 'جميع الموظفين مقيمين',
      columnsCount: 7
    });
  }
}

/**
 * تهيئة نظام الصفحات لجدول الإجازات
 */
function initializeVacationTablePagination() {
  if (document.getElementById('vacationsTableBody')) {
    createTablePagination('vacations-table', {
      paginationContainerId: 'vacationsPaginationContainer',
      itemsPerPageSelectId: 'vacationsItemsPerPageSelect',
      renderRowFunction: renderVacationRow,
      noDataMessage: 'لا توجد بيانات إجازات للعرض',
      columnsCount: 10
    });
  }
}

/**
 * تهيئة نظام الصفحات لجدول العهد
 */
function initializeCustodyTablePagination() {
  if (document.getElementById('custodyTableBody')) {
    createTablePagination('custody-table', {
      paginationContainerId: 'custodyPaginationContainer',
      itemsPerPageSelectId: 'custodyItemsPerPageSelect',
      renderRowFunction: renderCustodyRow,
      noDataMessage: 'لا توجد بيانات عهد للعرض',
      columnsCount: 9
    });
  }
}

/**
 * تهيئة جميع أنظمة الصفحات
 */
function initializeAllPagination() {
  // انتظار تحميل DOM
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initializeAllPagination, 100);
    });
    return;
  }

  // تهيئة الأنظمة المختلفة
  initializeMainTablePagination();
  initializeTrainingTablePagination();
  initializeEvaluationTablePagination();
  initializeVacationTablePagination();
  initializeCustodyTablePagination();
}

// تشغيل التهيئة
initializeAllPagination();

// تصدير الدوال للاستخدام العام
window.UniversalPagination = {
  createTablePagination,
  initializeAllPagination,
  renderEmployeeRow,
  renderTrainingRow,
  renderEvaluationRow,
  renderVacationRow,
  renderCustodyRow
};
