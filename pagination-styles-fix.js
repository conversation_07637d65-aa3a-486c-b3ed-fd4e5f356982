/**
 * إصلاح تعارضات تنسيقات نظام الصفحات
 * Fix for pagination styles conflicts
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    function waitForDOM(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    // إضافة تنسيقات CSS برمجياً
    function addPaginationStyles() {
        const styleId = 'pagination-styles-fix';
        
        // تجنب الإضافة المتكررة
        if (document.getElementById(styleId)) {
            return;
        }

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            /* إصلاح تعارضات نظام الصفحات */
            div.table-controls,
            div.filter-actions,
            div.logs-actions,
            div.actions-bar {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                flex-wrap: wrap !important;
                gap: 12px !important;
                margin-bottom: 16px !important;
            }

            div.table-controls div.items-per-page,
            div.filter-actions div.items-per-page,
            div.logs-actions div.items-per-page,
            div.actions-bar div.items-per-page {
                display: flex !important;
                align-items: center !important;
                gap: 8px !important;
                margin-left: auto !important;
                flex-shrink: 0 !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;
            }

            div.table-controls div.items-per-page label,
            div.filter-actions div.items-per-page label,
            div.logs-actions div.items-per-page label,
            div.actions-bar div.items-per-page label {
                font-size: 0.9rem !important;
                color: #333 !important;
                white-space: nowrap !important;
                font-weight: 500 !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            div.table-controls div.items-per-page select,
            div.filter-actions div.items-per-page select,
            div.logs-actions div.items-per-page select,
            div.actions-bar div.items-per-page select {
                padding: 6px 12px !important;
                border: 1px solid #ddd !important;
                border-radius: 6px !important;
                background: #ffffff !important;
                color: #333 !important;
                font-size: 0.9rem !important;
                cursor: pointer !important;
                min-width: 80px !important;
                font-family: inherit !important;
                margin: 0 !important;
            }

            div.table-controls div.items-per-page select:focus,
            div.filter-actions div.items-per-page select:focus,
            div.logs-actions div.items-per-page select:focus,
            div.actions-bar div.items-per-page select:focus {
                outline: none !important;
                border-color: #3498db !important;
                box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2) !important;
            }

            .pagination-container {
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                gap: 8px !important;
                margin-top: 20px !important;
                padding: 16px 0 !important;
                flex-wrap: wrap !important;
                clear: both !important;
            }

            .pagination-btn {
                background: #ffffff !important;
                border: 1px solid #ddd !important;
                color: #333 !important;
                padding: 8px 12px !important;
                border-radius: 6px !important;
                cursor: pointer !important;
                font-size: 0.9rem !important;
                transition: all 0.2s ease !important;
                min-width: 40px !important;
                height: 40px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                gap: 4px !important;
                font-family: inherit !important;
                text-decoration: none !important;
            }

            .pagination-btn:hover:not(:disabled) {
                background: #3498db !important;
                color: #ffffff !important;
                border-color: #3498db !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            }

            .pagination-btn.active {
                background: #3498db !important;
                color: #ffffff !important;
                border-color: #3498db !important;
                font-weight: bold !important;
                box-shadow: 0 2px 4px rgba(0,0,0,0.15) !important;
            }

            .pagination-btn:disabled {
                background: #f5f5f5 !important;
                color: #ccc !important;
                cursor: not-allowed !important;
                border-color: #e0e0e0 !important;
            }

            .pagination-btn:disabled:hover {
                transform: none !important;
                box-shadow: none !important;
            }

            .pagination-btn.prev-btn,
            .pagination-btn.next-btn {
                padding: 8px 16px !important;
                font-weight: 500 !important;
                min-width: auto !important;
            }

            .pagination-dots {
                color: #333 !important;
                font-weight: bold !important;
                padding: 0 4px !important;
                display: flex !important;
                align-items: center !important;
                height: 40px !important;
                user-select: none !important;
            }

            .page-info {
                background: #f8f9fa !important;
                color: #333 !important;
                padding: 8px 16px !important;
                border-radius: 6px !important;
                font-size: 0.9rem !important;
                margin-right: 16px !important;
                border: 1px solid #ddd !important;
                white-space: nowrap !important;
                font-weight: 500 !important;
            }

            /* تنسيقات متجاوبة */
            @media (max-width: 768px) {
                div.table-controls,
                div.filter-actions,
                div.actions-bar {
                    flex-direction: column !important;
                    align-items: stretch !important;
                    gap: 8px !important;
                }

                div.table-controls div.items-per-page,
                div.filter-actions div.items-per-page,
                div.logs-actions div.items-per-page,
                div.actions-bar div.items-per-page {
                    flex-direction: column !important;
                    align-items: flex-start !important;
                    gap: 4px !important;
                    margin-left: 0 !important;
                    margin-top: 8px !important;
                    width: 100% !important;
                }

                div.table-controls div.items-per-page select,
                div.filter-actions div.items-per-page select,
                div.logs-actions div.items-per-page select,
                div.actions-bar div.items-per-page select {
                    width: 100% !important;
                    font-size: 0.8rem !important;
                    padding: 4px 8px !important;
                }

                .pagination-container {
                    flex-direction: column !important;
                    gap: 4px !important;
                    padding: 12px 0 !important;
                }

                .pagination-btn {
                    padding: 6px 8px !important;
                    font-size: 0.8rem !important;
                    min-width: 32px !important;
                    height: 32px !important;
                }

                .page-info {
                    order: -1 !important;
                    width: 100% !important;
                    text-align: center !important;
                    margin-bottom: 8px !important;
                    margin-right: 0 !important;
                    font-size: 0.8rem !important;
                    padding: 6px 12px !important;
                }
            }
        `;

        // إضافة التنسيقات إلى head
        document.head.appendChild(style);
    }

    // إصلاح العناصر الموجودة
    function fixExistingElements() {
        // إصلاح عناصر items-per-page
        const itemsPerPageElements = document.querySelectorAll('.items-per-page');
        itemsPerPageElements.forEach(element => {
            element.style.display = 'flex';
            element.style.alignItems = 'center';
            element.style.gap = '8px';
            element.style.marginLeft = 'auto';
            element.style.flexShrink = '0';
        });

        // إصلاح حاويات التحكم
        const controlContainers = document.querySelectorAll('.table-controls, .filter-actions, .logs-actions, .actions-bar');
        controlContainers.forEach(container => {
            container.style.display = 'flex';
            container.style.justifyContent = 'space-between';
            container.style.alignItems = 'center';
            container.style.flexWrap = 'wrap';
            container.style.gap = '12px';
            container.style.marginBottom = '16px';
        });

        // إصلاح حاويات نظام الصفحات
        const paginationContainers = document.querySelectorAll('.pagination-container');
        paginationContainers.forEach(container => {
            container.style.display = 'flex';
            container.style.justifyContent = 'center';
            container.style.alignItems = 'center';
            container.style.gap = '8px';
            container.style.marginTop = '20px';
            container.style.padding = '16px 0';
            container.style.flexWrap = 'wrap';
            container.style.clear = 'both';
        });
    }

    // تشغيل الإصلاحات
    function initializeFixes() {
        addPaginationStyles();
        fixExistingElements();
        
        // مراقبة التغييرات في DOM
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    fixExistingElements();
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // تشغيل الإصلاحات عند تحميل الصفحة
    waitForDOM(initializeFixes);

    // تشغيل الإصلاحات عند تحميل النافذة (للتأكد)
    window.addEventListener('load', initializeFixes);

    // تصدير للاستخدام العام
    window.PaginationStylesFix = {
        addPaginationStyles,
        fixExistingElements,
        initializeFixes
    };

})();
