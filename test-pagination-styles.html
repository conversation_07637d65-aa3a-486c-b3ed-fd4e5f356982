<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنسيقات نظام الصفحات</title>
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="pagination-styles.css">
</head>
<body>
    <div class="container">
        <h1>اختبار تنسيقات نظام الصفحات</h1>
        
        <!-- اختبار table-controls -->
        <div class="section">
            <h2>اختبار table-controls</h2>
            <div class="table-controls">
                <button class="export-btn">تصدير</button>
                <div class="items-per-page">
                    <label for="test1">عدد العناصر في الصفحة:</label>
                    <select id="test1">
                        <option value="25">25</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- اختبار filter-actions -->
        <div class="section">
            <h2>اختبار filter-actions</h2>
            <div class="filter-actions">
                <button class="search-btn">بحث</button>
                <button class="reset-btn">إعادة تعيين</button>
                <div class="items-per-page">
                    <label for="test2">عدد العناصر في الصفحة:</label>
                    <select id="test2">
                        <option value="25">25</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- اختبار actions-bar -->
        <div class="section">
            <h2>اختبار actions-bar</h2>
            <div class="actions-bar">
                <button class="add-btn">إضافة جديد</button>
                <div class="items-per-page">
                    <label for="test3">عدد العناصر في الصفحة:</label>
                    <select id="test3">
                        <option value="25">25</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- اختبار جدول مع نظام الصفحات -->
        <div class="section">
            <h2>اختبار الجدول مع نظام الصفحات</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الاسم</th>
                        <th>القسم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>أحمد محمد</td>
                        <td>تقنية المعلومات</td>
                        <td>
                            <button class="view-btn">عرض</button>
                            <button class="edit-btn">تعديل</button>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>فاطمة علي</td>
                        <td>الموارد البشرية</td>
                        <td>
                            <button class="view-btn">عرض</button>
                            <button class="edit-btn">تعديل</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- نظام الصفحات -->
            <div class="pagination-container">
                <button class="pagination-btn prev-btn" disabled>
                    <i class="fas fa-chevron-right"></i> السابق
                </button>
                
                <button class="pagination-btn page-btn active">1</button>
                <button class="pagination-btn page-btn">2</button>
                <button class="pagination-btn page-btn">3</button>
                <span class="pagination-dots">...</span>
                <button class="pagination-btn page-btn">10</button>
                
                <button class="pagination-btn next-btn">
                    التالي <i class="fas fa-chevron-left"></i>
                </button>
                
                <div class="page-info">عرض 1 - 50 من 500 عنصر</div>
            </div>
        </div>
    </div>

    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .export-btn, .search-btn, .reset-btn, .add-btn, .view-btn, .edit-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .export-btn { background: #28a745; color: white; }
        .search-btn { background: #007bff; color: white; }
        .reset-btn { background: #6c757d; color: white; }
        .add-btn { background: #17a2b8; color: white; }
        .view-btn { background: #ffc107; color: black; }
        .edit-btn { background: #fd7e14; color: white; }
    </style>
</body>
</html>
