<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>تعديل بيانات الموظف</title>
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="edit.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="permissions.js" defer></script>
</head>
<body class="edit-page">

  <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="dashboard.html" class="dashboard-btn">
      <i class="fas fa-home"></i>
      <span>العودة للوحة التحكم</span>
    </a>
  </div>

  <!-- المحتوى الرئيسي -->
  <div class="main-content full-width" id="mainContent">
    <h1 class="form-title">تعديل بيانات الموظف</h1>
    <form id="editEmployeeForm" class="modern-form">
      <div class="form-grid">
        <div class="form-group">
          <label>كود الموظف</label>
          <input type="text" name="code" required readonly />
        </div>
        <div class="form-group">
          <label>الاسم الكامل</label>
          <input type="text" name="full_name" required />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>الإدارة</label>
          <input type="text" name="department" />
        </div>
        <div class="form-group">
          <label>الوظيفة</label>
          <input type="text" name="job_title" />
        </div>
        <div class="form-group">
          <label>تاريخ التعيين <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="hire_date" />
        </div>
        <div class="form-group">
          <label>العنوان</label>
          <input type="text" name="address" />
        </div>
        <div class="form-group">
          <label>المؤهل</label>
          <input type="text" name="qualification" />
        </div>
        <div class="form-group">
          <label>التليفون</label>
          <input type="text" name="phone" />
        </div>
        <div class="form-group">
          <label>تاريخ الميلاد <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="birth_date" />
        </div>
        <div class="form-group">
          <label>الحالة الاجتماعية</label>
          <input type="text" name="marital_status" />
        </div>
        <div class="form-group">
          <label>عدد الأبناء</label>
          <input type="text" name="children" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>الرقم القومي</label>
          <input type="text" name="national_id" />
        </div>
        <div class="form-group">
          <label>التأمين التكافلي</label>
          <input type="text" name="social_insurance" />
        </div>
        <div class="form-group">
          <label>الرقم التأميني</label>
          <input type="text" name="insurance_number" />
        </div>
        <div class="form-group">
          <label>جهة التأمين</label>
          <input type="text" name="insurance_entity" />
        </div>
        <div class="form-group">
          <label>تاريخ التأمين عليه <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="insurance_start" />
        </div>
        <div class="form-group">
          <label>المهنة في التأمينات</label>
          <input type="text" name="insurance_job" />
        </div>
        <div class="form-group">
          <label>راتب التأمينات</label>
          <input type="text" name="insurance_salary" />
        </div>
        <div class="form-group">
          <label>ما يتحمله العامل</label>
          <input type="text" name="worker_cost" />
        </div>
        <div class="form-group">
          <label>ما تتحمله الشركة</label>
          <input type="text" name="company_cost" />
        </div>
        <div class="form-group">
          <label>الأجر الشامل</label>
          <input type="text" name="total_salary" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>رقم البطاقة الصحية</label>
          <input type="text" name="health_card" />
        </div>
        <div class="form-group">
          <label>قياس المهارة</label>
          <input type="text" name="skill_level" />
        </div>
        <div class="form-group">
          <label>تاريخ بداية قياس المهارة <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="skill_start" />
        </div>
        <div class="form-group">
          <label>تاريخ انتهاء قياس المهارة <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="skill_end" />
        </div>
        <div class="form-group">
          <label>الوقت المتبقى على انتهاء قياس المهارة</label>
          <input type="text" name="skill_remaining" />
        </div>
        <div class="form-group">
          <label>مهنة قياس المهارة</label>
          <input type="text" name="skill_job" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>رصيد الإجازات</label>
          <input type="text" name="leave_balance" readonly style="background-color: #f5f5f5; cursor: not-allowed;" title="هذا الحقل محسوب تلقائياً ولا يمكن تعديله" />
        </div>
        <div class="form-group">
          <label>الإجازات المستخدمة</label>
          <input type="text" name="leave_used" readonly style="background-color: #f5f5f5; cursor: not-allowed;" title="هذا الحقل محسوب تلقائياً ولا يمكن تعديله" />
        </div>
        <div class="form-group">
          <label>الإجازات المتبقية</label>
          <input type="text" name="leave_remaining" readonly style="background-color: #f5f5f5; cursor: not-allowed;" title="هذا الحقل محسوب تلقائياً ولا يمكن تعديله" />
        </div>
        <div class="form-group">
          <label>ذوي الهمم</label>
          <input type="text" name="special_needs" />
        </div>
        <hr class="form-separator">

        <!-- حاوية أزرار الصورة والمستندات -->
        <div class="upload-buttons-container">
          <!-- قسم الصورة -->
          <div class="photo-upload-section">
            <h3 class="upload-section-title">
              <i class="fas fa-camera"></i> صورة الموظف
            </h3>
            <div class="current-photo-container" id="currentPhotoContainer" style="display: none;">
              <div class="current-photo-preview">
                <img id="currentPhotoImg" src="" alt="صورة الموظف الحالية">
                <div class="photo-actions">
                  <button type="button" class="btn-danger" onclick="deleteCurrentPhoto()">
                    <i class="fas fa-trash"></i> حذف الصورة
                  </button>
                </div>
              </div>
            </div>
            <div class="file-upload-container">
              <input type="file" id="employeePhoto" name="photo" accept="image/jpeg,image/jpg,image/png" class="file-input">
              <label for="employeePhoto" class="file-upload-label">
                <i class="fas fa-camera"></i>
                <span id="photoUploadText">اختر صورة جديدة</span>
              </label>
              <div class="file-preview" id="photoPreview" style="display: none;">
                <img id="photoPreviewImg" src="" alt="معاينة الصورة">
                <button type="button" class="remove-file-btn" onclick="removePhotoPreview()">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
            <small class="file-help-text">يُسمح بملفات JPG و PNG فقط، الحد الأقصى 5 ميجابايت</small>
          </div>

          <!-- قسم المستندات -->
          <div class="documents-upload-section">
            <h3 class="upload-section-title">
              <i class="fas fa-file-alt"></i> مستندات الموظف
            </h3>
            <div class="current-documents-container" id="currentDocumentsContainer">
              <div class="current-documents-list" id="currentDocumentsList"></div>
            </div>
            <div class="file-upload-container">
              <input type="file" id="employeeDocuments" name="documents" multiple
                     accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt" class="file-input">
              <label for="employeeDocuments" class="file-upload-label">
                <i class="fas fa-file-upload"></i>
                <span>إضافة مستندات جديدة</span>
              </label>
              <div class="documents-preview" id="documentsPreview"></div>
            </div>
            <small class="file-help-text">يُسمح بملفات PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, TXT، الحد الأقصى 10 ميجابايت لكل ملف</small>
          </div>
        </div>
      </div>
      <div class="form-actions">
        <button type="submit" class="save-btn">حفظ التعديلات</button>
        <button type="button" id="refreshBtn" class="refresh-btn"><i class="fas fa-sync-alt"></i> تحديث البيانات</button>
        <button type="button" onclick="cancelEdit()" class="cancel-btn">إلغاء</button>
      </div>
    </form>
  </div>

  <script src="dateUtils.js"></script>
  <script>
    const API_URL = localStorage.getItem('serverUrl') || 'http://localhost:5500/api';
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    
    if (!code) {
      alert('لم يتم تحديد كود الموظف');
      window.location.href = 'dashboard.html';
    }

    // دالة لتحميل بيانات الموظف
    async function loadEmployeeData() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${code}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (!response.ok) throw new Error('فشل في جلب البيانات');
        return await response.json();
      } catch (error) {
        console.error('Error:', error);
        alert('فشل في جلب بيانات الموظف');
        window.location.href = 'dashboard.html';
        return null;
      }
    }

    // دالة لتحميل صورة الموظف
    async function loadEmployeePhoto() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${code}/photo`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const photo = await response.json();
          const container = document.getElementById('currentPhotoContainer');
          const img = document.getElementById('currentPhotoImg');

          img.src = `${API_URL.replace('/api', '')}/api/files/${photo.photo_path}`;
          container.style.display = 'block';

          document.getElementById('photoUploadText').textContent = 'استبدال الصورة';
        }
      } catch (error) {
        console.log('لا توجد صورة للموظف');
      }
    }

    // دالة لتحميل مستندات الموظف
    async function loadEmployeeDocuments() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${code}/documents`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const documents = await response.json();
          const container = document.getElementById('currentDocumentsList');

          container.innerHTML = '';

          documents.forEach(doc => {
            const docItem = document.createElement('div');
            docItem.className = 'current-document-item';
            docItem.innerHTML = `
              <div class="document-info">
                <i class="fas fa-file"></i>
                <span class="document-name">${doc.display_name}</span>
                <span class="document-size">(${formatFileSize(doc.file_size)})</span>
              </div>
              <div class="document-actions">
                <button type="button" class="btn-view" onclick="viewDocument('${doc.document_path}')">
                  <i class="fas fa-eye"></i> عرض
                </button>
                <button type="button" class="btn-danger" onclick="deleteDocument(${doc.id})">
                  <i class="fas fa-trash"></i> حذف
                </button>
              </div>
            `;
            container.appendChild(docItem);
          });
        }
      } catch (error) {
        console.log('لا توجد مستندات للموظف');
      }
    }

    // حذف الصورة الحالية
    async function deleteCurrentPhoto() {
      if (!confirm('هل أنت متأكد من حذف الصورة؟')) return;

      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${code}/photo`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          document.getElementById('currentPhotoContainer').style.display = 'none';
          document.getElementById('photoUploadText').textContent = 'اختر صورة الموظف';
          alert('تم حذف الصورة بنجاح');
        } else {
          alert('فشل في حذف الصورة');
        }
      } catch (error) {
        console.error('خطأ في حذف الصورة:', error);
        alert('حدث خطأ أثناء حذف الصورة');
      }
    }

    // حذف مستند
    async function deleteDocument(documentId) {
      if (!confirm('هل أنت متأكد من حذف هذا المستند؟')) return;

      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${code}/documents/${documentId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          loadEmployeeDocuments(); // إعادة تحميل المستندات
          alert('تم حذف المستند بنجاح');
        } else {
          alert('فشل في حذف المستند');
        }
      } catch (error) {
        console.error('خطأ في حذف المستند:', error);
        alert('حدث خطأ أثناء حذف المستند');
      }
    }

    // عرض مستند
    function viewDocument(documentPath) {
      const url = `${API_URL.replace('/api', '')}/api/files/${documentPath}`;
      window.open(url, '_blank');
    }

    // تنسيق حجم الملف
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // دالة لحساب رصيد الإجازات
    function calculateLeaveBalance(hireDate) {
      if (!hireDate) return { balance: 7, used: 0, remaining: 7 };

      try {
        const hire = new Date(hireDate);
        const now = new Date();

        // حساب الفرق بالسنوات والأشهر
        let yearDiff = now.getFullYear() - hire.getFullYear();
        let monthDiff = now.getMonth() - hire.getMonth();

        if (monthDiff < 0) {
          yearDiff--;
          monthDiff += 12;
        }

        // حساب المدة بالأشهر للموظفين الجدد
        const totalMonths = yearDiff * 12 + monthDiff;
        const monthsOfService = totalMonths >= 0 ? totalMonths : 0;

        // تحديد رصيد الإجازات بناءً على مدة الخدمة
        let leaveBalance = 0;

        if (monthsOfService < 6) {
          // أقل من 6 أشهر: 7 أيام
          leaveBalance = 7;
        } else if (yearDiff < 10) {
          // من 6 أشهر إلى 10 سنوات: 21 يوم
          leaveBalance = 21;
        } else {
          // أكثر من 10 سنوات: 30 يوم
          leaveBalance = 30;
        }

        return {
          balance: leaveBalance,
          used: 0, // سيتم تحديثه من الخادم
          remaining: leaveBalance,
          yearsOfService: yearDiff,
          monthsOfService: monthsOfService
        };

      } catch (error) {
        console.error('خطأ في حساب رصيد الإجازات:', error);
        return { balance: 7, used: 0, remaining: 7 };
      }
    }

    // دالة لتحديث بيانات الإجازات من الخادم
    async function updateLeaveData(employeeCode) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${employeeCode}/leave-balance`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const leaveData = await response.json();

          // تحديث الحقول
          const form = document.getElementById('editEmployeeForm');
          if (form.elements['leave_balance']) {
            form.elements['leave_balance'].value = leaveData.calculated_balance || leaveData.current_balance || 0;
          }
          if (form.elements['leave_used']) {
            form.elements['leave_used'].value = leaveData.used_days || leaveData.current_used || 0;
          }
          if (form.elements['leave_remaining']) {
            form.elements['leave_remaining'].value = leaveData.remaining_days || leaveData.current_remaining || 0;
          }

          console.log('تم تحديث بيانات الإجازات من الخادم:', leaveData);
        } else {
          console.warn('فشل في تحميل بيانات الإجازات من الخادم');
        }
      } catch (error) {
        console.warn('خطأ في تحميل بيانات الإجازات:', error);
      }
    }

    // دالة لملء النموذج ببيانات الموظف
    async function fillFormWithEmployeeData(employee) {
      const form = document.getElementById('editEmployeeForm');

      // قائمة حقول التاريخ التي تحتاج معالجة خاصة
      const dateFields = [
        'birth_date', 'hire_date', 'insurance_start', 'skill_start',
        'skill_end', 'vacation_date', 'start_date', 'end_date',
        'resignation_date', 'work_end_date', 'delivery_date',
        'penalty_date', 'evaluation_date'
      ];

      // Fill form with employee data
      Object.keys(employee).forEach(key => {
        const input = form.elements[key];
        if (input) {
          if (dateFields.includes(key)) {
            // معالجة خاصة لحقول التاريخ
            input.value = DateUtils.formatDateForInput(employee[key]) || '';
          } else {
            input.value = employee[key] || '';
          }
        }
      });

      // تحديث بيانات الإجازات من الخادم
      if (employee.code) {
        await updateLeaveData(employee.code);
      } else {
        // حساب رصيد الإجازات محلياً إذا لم يتوفر من الخادم
        const leaveData = calculateLeaveBalance(employee.hire_date);
        if (form.elements['leave_balance']) {
          form.elements['leave_balance'].value = employee.leave_balance || leaveData.balance;
        }
        if (form.elements['leave_used']) {
          form.elements['leave_used'].value = employee.leave_used || leaveData.used;
        }
        if (form.elements['leave_remaining']) {
          form.elements['leave_remaining'].value = employee.leave_remaining || leaveData.remaining;
        }
      }
    }

    // تحميل بيانات الموظف عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', async function() {
      const employee = await loadEmployeeData();
      if (employee) {
        await fillFormWithEmployeeData(employee);
      }

      // تحميل الصورة والمستندات
      await loadEmployeePhoto();
      await loadEmployeeDocuments();

      // إضافة معالج حدث لزر التحديث
      document.getElementById('refreshBtn').addEventListener('click', async function() {
        const employee = await loadEmployeeData();
        if (employee) {
          await fillFormWithEmployeeData(employee);
          await loadEmployeePhoto();
          await loadEmployeeDocuments();
          alert('تم تحديث البيانات بنجاح');
        }
      });

      // معالجة معاينة الصورة الجديدة
      document.getElementById('employeePhoto').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('photoPreview');
        const previewImg = document.getElementById('photoPreviewImg');

        if (file) {
          if (!file.type.match('image.*')) {
            alert('يرجى اختيار ملف صورة صحيح');
            e.target.value = '';
            return;
          }

          if (file.size > 5 * 1024 * 1024) {
            alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
            e.target.value = '';
            return;
          }

          const reader = new FileReader();
          reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
          };
          reader.readAsDataURL(file);
        } else {
          preview.style.display = 'none';
        }
      });

      // معالجة معاينة المستندات الجديدة
      document.getElementById('employeeDocuments').addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        const preview = document.getElementById('documentsPreview');

        preview.innerHTML = '';

        files.forEach((file, index) => {
          if (file.size > 10 * 1024 * 1024) {
            alert(`حجم الملف "${file.name}" يجب أن يكون أقل من 10 ميجابايت`);
            return;
          }

          const fileItem = document.createElement('div');
          fileItem.className = 'document-preview-item';
          fileItem.innerHTML = `
            <div class="document-info">
              <i class="fas fa-file"></i>
              <span class="document-name">${file.name}</span>
              <span class="document-size">(${formatFileSize(file.size)})</span>
            </div>
            <button type="button" class="remove-document-btn" onclick="removeNewDocument(${index})">
              <i class="fas fa-times"></i>
            </button>
          `;
          preview.appendChild(fileItem);
        });
      });
    });

    // إزالة معاينة الصورة الجديدة
    function removePhotoPreview() {
      document.getElementById('employeePhoto').value = '';
      document.getElementById('photoPreview').style.display = 'none';
    }

    // إزالة مستند جديد من المعاينة
    function removeNewDocument(index) {
      const input = document.getElementById('employeeDocuments');
      const dt = new DataTransfer();
      const files = Array.from(input.files);

      files.forEach((file, i) => {
        if (i !== index) {
          dt.items.add(file);
        }
      });

      input.files = dt.files;
      input.dispatchEvent(new Event('change'));
    }

    // Handle form submission
    document.getElementById('editEmployeeForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      try {
        // حفظ البيانات الأساسية أولاً
        const formData = new FormData(e.target);
        const data = {};

        // قائمة حقول التاريخ التي تحتاج معالجة خاصة
        const dateFields = [
          'birth_date', 'hire_date', 'insurance_start', 'skill_start',
          'skill_end', 'vacation_date', 'start_date', 'end_date',
          'resignation_date', 'work_end_date', 'delivery_date',
          'penalty_date', 'evaluation_date'
        ];

        formData.forEach((value, key) => {
          if (key !== 'photo' && key !== 'documents') {
            // معالجة خاصة لحقول التاريخ (تجنب مشاكل المنطقة الزمنية)
            if (dateFields.includes(key) && value) {
              // استخدام التاريخ مباشرة بدون تحويل لتجنب مشاكل المنطقة الزمنية
              data[key] = value;
            } else {
              data[key] = value;
            }
          }
        });

        console.log('📅 بيانات الموظف مع التواريخ المحولة:', data);

        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${code}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(data)
        });

        if (!response.ok) throw new Error('فشل في حفظ التعديلات');

        // رفع الصورة الجديدة إذا تم اختيارها
        const photoFile = document.getElementById('employeePhoto').files[0];
        if (photoFile) {
          const photoFormData = new FormData();
          photoFormData.append('photo', photoFile);

          try {
            const token = localStorage.getItem('token');
            const photoResponse = await fetch(`${API_URL}/employees/${code}/photo`, {
              method: "POST",
              headers: {
                'Authorization': `Bearer ${token}`
              },
              body: photoFormData
            });

            if (!photoResponse.ok) {
              console.warn('فشل في رفع الصورة الجديدة');
            }
          } catch (photoError) {
            console.warn('خطأ في رفع الصورة:', photoError);
          }
        }

        // رفع المستندات الجديدة إذا تم اختيارها
        const documentFiles = document.getElementById('employeeDocuments').files;
        if (documentFiles.length > 0) {
          const documentsFormData = new FormData();

          for (let i = 0; i < documentFiles.length; i++) {
            documentsFormData.append('documents', documentFiles[i]);
            documentsFormData.append('displayNames', documentFiles[i].name);
          }

          try {
            const token = localStorage.getItem('token');
            const documentsResponse = await fetch(`${API_URL}/employees/${code}/documents`, {
              method: "POST",
              headers: {
                'Authorization': `Bearer ${token}`
              },
              body: documentsFormData
            });

            if (!documentsResponse.ok) {
              console.warn('فشل في رفع بعض المستندات الجديدة');
            }
          } catch (documentsError) {
            console.warn('خطأ في رفع المستندات:', documentsError);
          }
        }

        alert('تم حفظ التعديلات بنجاح');
        window.location.href = 'index.html';

      } catch (error) {
        console.error('Error:', error);
        alert('فشل في حفظ التعديلات: ' + error.message);
      }
    });
  </script>



<script>
// دالة تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح البيانات المحفوظة
        localStorage.removeItem('token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('activeSection');
        localStorage.removeItem('userName');
        
        // إعادة التوجيه لصفحة تسجيل الدخول
        window.location.href = 'login.html';
    }
}

// تم حذف دالة updateSidebarUserInfo المتعارضة - يتم التحكم بها من sidebar-standalone.js فقط

// دالة إلغاء التعديل
function cancelEdit() {
    if (confirm('هل أنت متأكد من إلغاء التعديل؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        // العودة إلى الصفحة السابقة أو الصفحة الرئيسية
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = 'dashboard.html';
        }
    }
}


</script>
</body>
</html>