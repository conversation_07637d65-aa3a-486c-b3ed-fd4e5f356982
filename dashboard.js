/* ===== إدارة لوحة التحكم والكروت ===== */

// ===== متغيرات عامة =====
let dashboardManager = null;

// ===== فئة إدارة لوحة التحكم =====
class DashboardManager {
    constructor() {
        this.cards = [];
        this.permissions = {};
        this.userInfo = {};
        this.init();
    }

    // ===== تهيئة النظام =====
    init() {
        this.loadUserData();
        this.loadPermissions();
        this.initializeCards();
        this.setupEventListeners();
        this.updateUserDisplay();
        this.applyPermissions();
    }

    // ===== تحميل بيانات المستخدم =====
    loadUserData() {
        try {
            const userInfoStr = localStorage.getItem('userInfo');
            const userName = localStorage.getItem('userName');
            const username = localStorage.getItem('username'); // إضافة البحث عن username أيضاً

            if (userInfoStr) {
                this.userInfo = JSON.parse(userInfoStr);
            }

            // استخدام userName أولاً، ثم username إذا لم يوجد userName
            if (userName) {
                this.userInfo.name = userName;
            } else if (username) {
                this.userInfo.name = username;
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المستخدم:', error);
            this.userInfo = { name: 'المستخدم الحالي' };
        }
    }

    // ===== تحميل الصلاحيات =====
    loadPermissions() {
        try {
            const permissionsStr = localStorage.getItem('permissions');
            if (permissionsStr) {
                this.permissions = JSON.parse(permissionsStr);
            }
        } catch (error) {
            console.error('خطأ في تحميل الصلاحيات:', error);
            this.permissions = {};
        }
    }

    // ===== تهيئة الكروت =====
    initializeCards() {
        const cardElements = document.querySelectorAll('.dashboard-card');
        this.cards = Array.from(cardElements).map(card => ({
            element: card,
            permission: card.getAttribute('data-permission'),
            href: card.getAttribute('data-href'),
            title: card.querySelector('.card-title')?.textContent || '',
            visible: true
        }));
    }

    // ===== إعداد مستمعي الأحداث =====
    setupEventListeners() {
        // إضافة مستمع النقر للكروت
        this.cards.forEach(card => {
            card.element.addEventListener('click', (e) => {
                this.handleCardClick(card, e);
            });

            // إضافة دعم لوحة المفاتيح
            card.element.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleCardClick(card, e);
                }
            });

            // جعل الكارت قابل للتركيز
            card.element.setAttribute('tabindex', '0');
        });

        // مستمع تغيير حجم النافذة لتحسين الأداء
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    // ===== معالجة النقر على الكارت =====
    handleCardClick(card, event) {
        // التحقق من الصلاحية
        if (!this.hasPermission(card.permission)) {
            this.showPermissionError(card.title);
            return;
        }

        // إضافة تأثير التحميل
        card.element.classList.add('loading');

        // التنقل إلى الصفحة
        if (card.href) {
            // تأخير بسيط لإظهار تأثير التحميل
            setTimeout(() => {
                window.location.href = card.href;
            }, 200);
        }
    }

    // ===== التحقق من الصلاحيات =====
    hasPermission(permission) {
        if (!permission) return true;

        // استخدام الدالة العامة للتحقق من الصلاحيات
        if (typeof hasPermission === 'function') {
            return hasPermission(permission);
        }

        // التحقق المحلي كبديل
        if (this.permissions[permission] === true) {
            return true;
        }

        // التحقق من الصلاحيات العامة
        if (permission.startsWith('view_') && this.permissions['can_view'] === true) {
            return true;
        }

        if (permission.startsWith('add_') && this.permissions['can_add'] === true) {
            return true;
        }

        return false;
    }

    // ===== تطبيق الصلاحيات على الكروت =====
    applyPermissions() {
        let visibleCount = 0;

        this.cards.forEach(card => {
            const hasAccess = this.hasPermission(card.permission);
            
            if (hasAccess) {
                card.element.classList.remove('hidden');
                card.visible = true;
                visibleCount++;
            } else {
                card.element.classList.add('hidden');
                card.visible = false;
            }
        });

        // إظهار رسالة إذا لم تكن هناك كروت مرئية
        if (visibleCount === 0) {
            this.showNoAccessMessage();
        }
    }

    // ===== تحديث عرض المستخدم =====
    updateUserDisplay() {
        const userNameElement = document.getElementById('userName');
        if (userNameElement) {
            // استخدام اسم المستخدم المحفوظ أو القيمة الافتراضية
            const displayName = this.userInfo.name || localStorage.getItem('username') || localStorage.getItem('userName') || 'المستخدم الحالي';
            userNameElement.textContent = displayName;
            console.log('تم تحديث عرض المستخدم:', displayName);
        }
    }

    // ===== إظهار رسالة خطأ الصلاحية =====
    showPermissionError(cardTitle) {
        const message = `عذراً، ليس لديك صلاحية للوصول إلى قسم "${cardTitle}"`;
        
        // إنشاء رسالة تنبيه مخصصة
        this.showNotification(message, 'error');
    }

    // ===== إظهار رسالة عدم وجود صلاحيات =====
    showNoAccessMessage() {
        const cardsGrid = document.getElementById('cardsGrid');
        if (cardsGrid) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'no-access-message';
            messageDiv.innerHTML = `
                <div class="no-access-content">
                    <i class="fas fa-lock"></i>
                    <h3>لا توجد أقسام متاحة</h3>
                    <p>عذراً، ليس لديك صلاحية للوصول إلى أي من الأقسام المتاحة.</p>
                    <p>يرجى التواصل مع المدير لمنحك الصلاحيات المناسبة.</p>
                </div>
            `;
            cardsGrid.appendChild(messageDiv);
        }
    }

    // ===== استخدام دالة showNotification من shared-utils.js =====

    // ===== معالجة تغيير حجم النافذة =====
    handleResize() {
        // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
    }

    // ===== إعادة تحميل الصلاحيات =====
    refreshPermissions() {
        this.loadPermissions();
        this.applyPermissions();
    }

    // ===== البحث في الكروت =====
    searchCards(query) {
        const searchTerm = query.toLowerCase().trim();

        this.cards.forEach(card => {
            const title = card.title.toLowerCase();
            const description = card.element.querySelector('.card-description')?.textContent.toLowerCase() || '';

            const matches = title.includes(searchTerm) || description.includes(searchTerm);
            const hasPermission = this.hasPermission(card.permission);

            if (matches && hasPermission) {
                card.element.style.display = 'block';
            } else {
                card.element.style.display = 'none';
            }
        });
    }


}

// ===== دالة تسجيل الخروج =====
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح البيانات المحفوظة
        localStorage.removeItem('token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('activeSection');
        localStorage.removeItem('userName');
        
        // إعادة التوجيه إلى صفحة تسجيل الدخول
        window.location.href = 'login.html';
    }
}

// ===== تهيئة النظام عند تحميل الصفحة =====
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود التوكن
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = 'login.html';
        return;
    }

    // تهيئة مدير لوحة التحكم
    dashboardManager = new DashboardManager();
    
    // تصدير المدير للاستخدام العام
    window.dashboardManager = dashboardManager;
});

// ===== تصدير الوظائف للاستخدام الخارجي =====
window.DashboardController = {
    refresh: function() {
        if (dashboardManager) {
            dashboardManager.refreshPermissions();
        }
    },
    search: function(query) {
        if (dashboardManager) {
            dashboardManager.searchCards(query);
        }
    }
};
