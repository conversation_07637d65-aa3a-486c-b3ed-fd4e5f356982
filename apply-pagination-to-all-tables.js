/**
 * سكريبت لتطبيق نظام الصفحات على جميع الجداول في المشروع
 * Script to apply pagination to all tables in the project
 */

// قائمة بجميع الجداول التي تحتاج نظام صفحات
const TABLES_CONFIG = {
  // الجدول الرئيسي للموظفين
  'employee-table': {
    tableBodyId: 'employeeTableBody',
    paginationContainerId: 'paginationContainer',
    itemsPerPageSelectId: 'itemsPerPageSelect',
    renderFunction: 'renderEmployeeRow',
    columnsCount: 6,
    noDataMessage: 'لا توجد بيانات موظفين للعرض'
  },

  // جدول التدريب
  'training-table': {
    tableBodyId: 'training-table',
    paginationContainerId: 'trainingPaginationContainer',
    itemsPerPageSelectId: 'trainingItemsPerPageSelect',
    renderFunction: 'renderTrainingRow',
    columnsCount: 10,
    noDataMessage: 'لا توجد بيانات تدريب للعرض'
  },

  // جدول تقارير التدريب
  'report-table': {
    tableBodyId: 'report-table',
    paginationContainerId: 'trainingReportPaginationContainer',
    itemsPerPageSelectId: 'trainingReportItemsPerPageSelect',
    renderFunction: 'renderTrainingReportRow',
    columnsCount: 8,
    noDataMessage: 'لا توجد بيانات في التقرير'
  },

  // جدول التقييمات
  'evaluation-table': {
    tableBodyId: 'evaluation-table',
    paginationContainerId: 'evaluationPaginationContainer',
    itemsPerPageSelectId: 'evaluationItemsPerPageSelect',
    renderFunction: 'renderEvaluationRow',
    columnsCount: 9,
    noDataMessage: 'لا توجد بيانات تقييم للعرض'
  },

  // جدول الموظفين غير المقيمين
  'unevaluated-employees-table': {
    tableBodyId: 'unevaluated-employees-table',
    paginationContainerId: 'unevaluatedPaginationContainer',
    itemsPerPageSelectId: 'unevaluatedItemsPerPageSelect',
    renderFunction: 'renderUnevaluatedEmployeeRow',
    columnsCount: 7,
    noDataMessage: 'جميع الموظفين مقيمين'
  },

  // جدول الإجازات
  'vacations-table': {
    tableBodyId: 'vacationsTableBody',
    paginationContainerId: 'vacationsPaginationContainer',
    itemsPerPageSelectId: 'vacationsItemsPerPageSelect',
    renderFunction: 'renderVacationRow',
    columnsCount: 10,
    noDataMessage: 'لا توجد بيانات إجازات للعرض'
  },

  // جدول العهد
  'custody-table': {
    tableBodyId: 'custodyTableBody',
    paginationContainerId: 'custodyPaginationContainer',
    itemsPerPageSelectId: 'custodyItemsPerPageSelect',
    renderFunction: 'renderCustodyRow',
    columnsCount: 9,
    noDataMessage: 'لا توجد بيانات عهد للعرض'
  },

  // جدول المساهمات
  'contributions-table': {
    tableBodyId: 'contributionsTableBody',
    paginationContainerId: 'contributionsPaginationContainer',
    itemsPerPageSelectId: 'contributionsItemsPerPageSelect',
    renderFunction: 'renderContributionRow',
    columnsCount: 8,
    noDataMessage: 'لا توجد بيانات مساهمات للعرض'
  },

  // جدول المكافآت والخصومات
  'rewards-deductions-table': {
    tableBodyId: 'rewardsDeductionsTableBody',
    paginationContainerId: 'rewardsDeductionsPaginationContainer',
    itemsPerPageSelectId: 'rewardsDeductionsItemsPerPageSelect',
    renderFunction: 'renderRewardDeductionRow',
    columnsCount: 7,
    noDataMessage: 'لا توجد بيانات مكافآت أو خصومات للعرض'
  },

  // جدول السلف
  'salary-advance-table': {
    tableBodyId: 'salaryAdvanceTableBody',
    paginationContainerId: 'salaryAdvancePaginationContainer',
    itemsPerPageSelectId: 'salaryAdvanceItemsPerPageSelect',
    renderFunction: 'renderSalaryAdvanceRow',
    columnsCount: 6,
    noDataMessage: 'لا توجد بيانات سلف للعرض'
  },

  // جدول الساعات الإضافية
  'extra-hours-table': {
    tableBodyId: 'extraHoursTableBody',
    paginationContainerId: 'extraHoursPaginationContainer',
    itemsPerPageSelectId: 'extraHoursItemsPerPageSelect',
    renderFunction: 'renderExtraHoursRow',
    columnsCount: 7,
    noDataMessage: 'لا توجد بيانات ساعات إضافية للعرض'
  },

  // جدول الموظف المثالي
  'ideal-employee-table': {
    tableBodyId: 'idealEmployeeTableBody',
    paginationContainerId: 'idealEmployeePaginationContainer',
    itemsPerPageSelectId: 'idealEmployeeItemsPerPageSelect',
    renderFunction: 'renderIdealEmployeeRow',
    columnsCount: 10,
    noDataMessage: 'لا توجد بيانات موظف مثالي للعرض'
  },

  // جدول الاستقالات
  'resignations-table': {
    tableBodyId: 'resignationsTableBody',
    paginationContainerId: 'resignationsPaginationContainer',
    itemsPerPageSelectId: 'resignationsItemsPerPageSelect',
    renderFunction: 'renderResignationRow',
    columnsCount: 8,
    noDataMessage: 'لا توجد بيانات استقالات للعرض'
  },

  // جدول المستخدمين
  'users-table': {
    tableBodyId: 'usersTableBody',
    paginationContainerId: 'usersPaginationContainer',
    itemsPerPageSelectId: 'usersItemsPerPageSelect',
    renderFunction: 'renderUserRow',
    columnsCount: 5,
    noDataMessage: 'لا توجد مستخدمين للعرض'
  },

  // جدول سجل الأنشطة
  'activity-log-table': {
    tableBodyId: 'activityLogTableBody',
    paginationContainerId: 'activityLogPaginationContainer',
    itemsPerPageSelectId: 'activityLogItemsPerPageSelect',
    renderFunction: 'renderActivityLogRow',
    columnsCount: 6,
    noDataMessage: 'لا توجد أنشطة للعرض'
  }
};

/**
 * دوال العرض المخصصة لكل نوع جدول
 */
const RENDER_FUNCTIONS = {
  renderEmployeeRow: (emp) => {
    const displayValue = (value) => value === null ? '-' : value;
    let actionsHTML = '';
    actionsHTML += `<button class="view-btn" data-id="${emp.code}">عرض</button>`;
    if (window.hasPermission && window.hasPermission('can_edit')) {
      actionsHTML += `<button class="edit-btn" data-id="${emp.code}">تعديل</button>`;
    }
    if (window.hasPermission && window.hasPermission('can_delete')) {
      actionsHTML += `<button class="delete-btn" data-id="${emp.code}">حذف</button>`;
    }
    return `<td>${displayValue(emp.code)}</td>
<td>${displayValue(emp.full_name)}</td>
<td>${displayValue(emp.department)}</td>
<td>${displayValue(emp.job_title)}</td>
<td>${emp.hire_date ? formatDate(emp.hire_date) : '-'}</td>
<td>${actionsHTML}</td>`;
  },

  renderTrainingRow: (training) => {
    const displayValue = (value) => value === null ? '-' : value;
    return `<td>${displayValue(training.employee_code)}</td>
<td>${displayValue(training.employee_name)}</td>
<td>${displayValue(training.department)}</td>
<td>${displayValue(training.course_name)}</td>
<td>${training.course_date ? formatDate(training.course_date) : '-'}</td>
<td>${displayValue(training.duration)}</td>
<td>${displayValue(training.training_type)}</td>
<td>${displayValue(training.cost)}</td>
<td>${displayValue(training.notes)}</td>
<td>
  <button class="edit-btn" data-id="${training.id}">تعديل</button>
  <button class="delete-btn" data-id="${training.id}">حذف</button>
</td>`;
  },

  renderTrainingReportRow: (training) => {
    const displayValue = (value) => value === null ? '-' : value;
    return `<td>${displayValue(training.employee_code)}</td>
<td>${displayValue(training.employee_name)}</td>
<td>${displayValue(training.department)}</td>
<td>${displayValue(training.course_name)}</td>
<td>${training.course_date ? formatDate(training.course_date) : '-'}</td>
<td>${displayValue(training.duration)}</td>
<td>${displayValue(training.training_type)}</td>
<td>${displayValue(training.cost)}</td>`;
  },

  renderEvaluationRow: (evaluation) => {
    const displayValue = (value) => value === null ? '-' : value;
    return `<td>${displayValue(evaluation.employee_code)}</td>
<td>${displayValue(evaluation.employee_name)}</td>
<td>${displayValue(evaluation.department)}</td>
<td>${displayValue(evaluation.evaluation_type)}</td>
<td>${evaluation.start_date ? formatDate(evaluation.start_date) : '-'}</td>
<td>${evaluation.end_date ? formatDate(evaluation.end_date) : '-'}</td>
<td>${displayValue(evaluation.score)}</td>
<td>${displayValue(evaluation.notes)}</td>
<td>
  <button class="edit-btn" data-id="${evaluation.id}">تعديل</button>
  <button class="delete-btn" data-id="${evaluation.id}">حذف</button>
</td>`;
  },

  renderUnevaluatedEmployeeRow: (emp) => {
    return `<td>${emp.code}</td>
<td>${emp.full_name}</td>
<td>${emp.department}</td>
<td>${emp.job_title}</td>
<td>${emp.hire_date ? formatDate(emp.hire_date) : '-'}</td>
<td>${emp.last_evaluation || '-'}</td>
<td>${emp.days_since_evaluation || '-'}</td>`;
  },

  renderVacationRow: (vacation) => {
    const displayValue = (value) => value === null ? '-' : value;
    return `<td>${displayValue(vacation.employee_code)}</td>
<td>${displayValue(vacation.employee_name)}</td>
<td>${displayValue(vacation.casual_leave)}</td>
<td>${displayValue(vacation.authorized_absence)}</td>
<td>${displayValue(vacation.unauthorized_absence)}</td>
<td>${displayValue(vacation.annual_leave)}</td>
<td>${displayValue(vacation.unpaid_leave)}</td>
<td>${displayValue(vacation.sick_leave)}</td>
<td>${displayValue(vacation.extra_leave)}</td>
<td>
  <button class="details-btn" data-id="${vacation.employee_code}">التفاصيل</button>
</td>`;
  },

  renderCustodyRow: (custody) => {
    const displayValue = (value) => value === null ? '-' : value;
    return `<td>${displayValue(custody.custody_code)}</td>
<td>${displayValue(custody.custody_name)}</td>
<td>${displayValue(custody.custody_type)}</td>
<td>${displayValue(custody.status)}</td>
<td>${displayValue(custody.quantity)}</td>
<td>${displayValue(custody.delivered_quantity)}</td>
<td>${displayValue(custody.remaining_quantity)}</td>
<td>${custody.created_at ? formatDate(custody.created_at) : '-'}</td>
<td>
  <button class="edit-btn" data-id="${custody.id}">تعديل</button>
  <button class="delete-btn" data-id="${custody.id}">حذف</button>
</td>`;
  }
};

/**
 * تهيئة نظام الصفحات لجدول معين
 */
function initializePaginationForTable(tableId) {
  const config = TABLES_CONFIG[tableId];
  if (!config) return null;

  const tableElement = document.getElementById(tableId);
  const tableBody = tableElement ? tableElement.querySelector('tbody') : document.getElementById(config.tableBodyId);
  const paginationContainer = document.getElementById(config.paginationContainerId);
  const itemsPerPageSelect = document.getElementById(config.itemsPerPageSelectId);

  if (!tableBody) {
    console.warn(`Table body not found for table: ${tableId}`);
    return null;
  }

  const renderFunction = RENDER_FUNCTIONS[config.renderFunction];
  
  const paginationSystem = new PaginationSystem({
    tableBody: tableBody,
    paginationContainer: paginationContainer,
    itemsPerPageSelect: itemsPerPageSelect,
    renderRowFunction: renderFunction,
    noDataMessage: config.noDataMessage,
    columnsCount: config.columnsCount,
    itemsPerPage: 50
  });

  // حفظ المثيل للاستخدام لاحقاً
  window.PaginationInstances = window.PaginationInstances || {};
  window.PaginationInstances[tableId] = paginationSystem;
  
  return paginationSystem;
}

/**
 * تهيئة جميع أنظمة الصفحات
 */
function initializeAllTablesPagination() {
  // انتظار تحميل DOM
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(initializeAllTablesPagination, 100);
    });
    return;
  }

  // تهيئة كل جدول
  Object.keys(TABLES_CONFIG).forEach(tableId => {
    try {
      initializePaginationForTable(tableId);
    } catch (error) {
      console.warn(`Failed to initialize pagination for table ${tableId}:`, error);
    }
  });
}

// تشغيل التهيئة
initializeAllTablesPagination();

// تصدير للاستخدام العام
window.TablesPaginationManager = {
  initializePaginationForTable,
  initializeAllTablesPagination,
  TABLES_CONFIG,
  RENDER_FUNCTIONS
};
