/* تنسيقات عصرية واحترافية لصفحة تعديل الموظف */

.edit-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: auto;
}

.main-content {
  padding: 20px;
  width: 100%;
  max-width: 100vw;
  margin: 0;
  box-sizing: border-box;
}

.form-title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.modern-form {
  width: 100%;
  margin: 0;
  padding: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  box-sizing: border-box;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
  width: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 15px;
  min-width: 0;
  width: 100%;
}

.form-group label {
  font-weight: bold;
  color: #333;
  font-size: 0.9rem;
}

.form-group input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
}

.form-group input:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.form-group input::placeholder {
  color: #999;
}

.form-group input[readonly] {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.form-separator {
  grid-column: 1 / -1;
  border: none;
  border-top: 1px solid #ddd;
  margin: 20px 0;
}

.form-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
  justify-content: center;
}

.save-btn {
  background-color: #4CAF50;
  color: white !important;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
  min-width: 100px;
}

.save-btn:hover {
  background-color: #45a049;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
  color: white !important;
}

.refresh-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
  min-width: 100px;
}

.refresh-btn:hover {
  background-color: #0b7dda;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

.cancel-btn {
  background-color: #f44336;
  color: white !important;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
  min-width: 100px;
}

.cancel-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.4);
  color: white !important;
}

.submit-btn {
  display: block;
  width: 200px;
  margin: 20px auto;
  padding: 12px 24px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: #357abd;
}

/* تنسيقات متجاوبة للشاشات المختلفة */
@media (min-width: 2000px) {
  .form-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}

@media (min-width: 1800px) and (max-width: 1999px) {
  .form-grid {
    grid-template-columns: repeat(7, 1fr);
  }
}

@media (min-width: 1600px) and (max-width: 1799px) {
  .form-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (min-width: 1400px) and (max-width: 1599px) {
  .form-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .form-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 900px) and (max-width: 1199px) {
  .form-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 600px) and (max-width: 899px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 599px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .main-content {
    padding: 15px;
  }

  .modern-form {
    padding: 20px;
  }

  .upload-buttons-container {
    flex-direction: column;
    gap: 15px;
  }

  .form-actions {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  .form-actions button {
    width: 100%;
    max-width: 200px;
  }
}

  .modern-form {
    padding: 15px;
  }
}

/* تنسيق رفع الملفات */
.file-upload-section {
  margin-bottom: 25px;
}

/* محاذاة أزرار الصورة والمستندات */
.upload-buttons-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  margin-bottom: 25px;
  width: 100%;
  box-sizing: border-box;
}

.photo-upload-section,
.documents-upload-section {
  flex: 1;
  min-width: 0;
}

.upload-section-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  font-size: 1rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 5px;
}

.file-upload-container {
  position: relative;
  margin-top: 8px;
}

.file-input {
  display: none;
}

.file-upload-label {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white !important;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 10px rgba(33, 150, 243, 0.3);
  text-decoration: none;
}

.file-upload-label:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(33, 150, 243, 0.4);
  color: white !important;
}

.file-upload-label i {
  font-size: 16px;
}

.file-help-text {
  display: block;
  margin-top: 8px;
  color: #666;
  font-size: 12px;
  font-style: italic;
}

/* الصورة الحالية */
.current-photo-container {
  margin-bottom: 15px;
}

.current-photo-preview {
  position: relative;
  display: inline-block;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: white;
  padding: 8px;
}

.current-photo-preview img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  display: block;
}

.photo-actions {
  margin-top: 10px;
  text-align: center;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.btn-view {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  margin-right: 5px;
  transition: all 0.3s ease;
}

.btn-view:hover {
  background: #0056b3;
}

/* المستندات الحالية */
.current-documents-container {
  margin-bottom: 15px;
}

.current-document-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.current-document-item:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.document-actions {
  display: flex;
  gap: 5px;
}

/* معاينة الصورة الجديدة */
.file-preview {
  position: relative;
  margin-top: 15px;
  display: inline-block;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: white;
  padding: 8px;
}

.file-preview img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  display: block;
}

.remove-file-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
}

.remove-file-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}

/* معاينة المستندات الجديدة */
.documents-preview {
  margin-top: 15px;
}

.document-preview-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.document-preview-item:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.document-info i {
  color: #6c757d;
  font-size: 16px;
}

.document-name {
  font-weight: 500;
  color: #495057;
}

.document-size {
  color: #6c757d;
  font-size: 12px;
}

.remove-document-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.3s ease;
}

.remove-document-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}
