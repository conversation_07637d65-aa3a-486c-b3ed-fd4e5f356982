CREATE TABLE `employees` (
  `code` int NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `department` varchar(255) DEFAULT NULL,
  `job_title` varchar(255) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `qualification` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `birth_date` date DEFAULT NULL,
  `marital_status` varchar(50) DEFAULT NULL,
  `children` varchar(10) DEFAULT NULL,
  `national_id` varchar(50) DEFAULT NULL,
  `social_insurance` varchar(50) DEFAULT NULL,
  `insurance_number` varchar(50) DEFAULT NULL,
  `insurance_entity` varchar(255) DEFAULT NULL,
  `insurance_start` date DEFAULT NULL,
  `insurance_job` varchar(255) DEFAULT NULL,
  `insurance_salary` varchar(50) DEFAULT NULL,
  `worker_cost` varchar(50) DEFAULT NULL,
  `company_cost` varchar(50) DEFAULT NULL,
  `total_salary` varchar(50) DEFAULT NULL,
  `health_card` varchar(50) DEFAULT NULL,
  `skill_level` varchar(50) DEFAULT NULL,
  `skill_start` date DEFAULT NULL,
  `skill_end` date DEFAULT NULL,
  `skill_remaining` varchar(50) DEFAULT NULL,
  `skill_job` varchar(255) DEFAULT NULL,
  `leave_balance` int DEFAULT '0',
  `leave_used` varchar(50) DEFAULT NULL,
  `leave_remaining` varchar(50) DEFAULT NULL,
  `special_needs` varchar(50) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `documents` varchar(255) DEFAULT NULL,
  `status` enum('نشط','مستقيل') DEFAULT 'نشط' COMMENT 'حالة الموظف',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci



CREATE TABLE `vacations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(50) DEFAULT NULL,
  `employee_name` varchar(255) DEFAULT NULL,
  `department` varchar(255) NOT NULL,
  `vacation_type` enum('casual','permission','absence','annual','unpaid','sick','official') NOT NULL,
  `official_type` varchar(50) DEFAULT NULL,
  `vacation_date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_employee_code` (`employee_code`),
  KEY `idx_department` (`department`),
  KEY `idx_vacation_date` (`vacation_date`),
  KEY `idx_vacation_type` (`vacation_type`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci





CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `employee_code` int DEFAULT NULL COMMENT 'كود الموظف المرتبط بالمستخدم',
  `permissions` JSON DEFAULT NULL COMMENT 'صلاحيات المستخدم',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `employee_code` (`employee_code`),
  KEY `idx_employee_code` (`employee_code`),
  CONSTRAINT `fk_users_employee` FOREIGN KEY (`employee_code`) REFERENCES `employees` (`code`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci



-- جداول العهد

-- جدول العهد الأساسي
CREATE TABLE `custody` (
  `id` int NOT NULL AUTO_INCREMENT,
  `custody_code` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` varchar(100) NOT NULL,
  `status` enum('جديدة','مستعمل','صيانة') DEFAULT 'جديدة',
  `quantity` int NOT NULL DEFAULT '1',
  `available_quantity` int NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `custody_code` (`custody_code`),
  KEY `idx_custody_code` (`custody_code`),
  KEY `idx_custody_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

-- جدول تسليم العهد
CREATE TABLE `custody_delivery` (
  `id` int NOT NULL AUTO_INCREMENT,
  `operation_number` varchar(20) NOT NULL,
  `employee_code` int NOT NULL,
  `department` varchar(100) DEFAULT NULL,
  `employee_name` varchar(255) NOT NULL,
  `custody_code` varchar(20) NOT NULL,
  `custody_name` varchar(255) NOT NULL,
  `custody_type` varchar(100) NOT NULL,
  `quantity` int NOT NULL DEFAULT '1',
  `delivery_date` date NOT NULL,
  `status` enum('مسلم','مسترجع') DEFAULT 'مسلم',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operation_number` (`operation_number`),
  KEY `employee_code` (`employee_code`),
  KEY `custody_code` (`custody_code`),
  CONSTRAINT `custody_delivery_ibfk_1` FOREIGN KEY (`employee_code`) REFERENCES `employees` (`code`) ON DELETE CASCADE,
  CONSTRAINT `custody_delivery_ibfk_2` FOREIGN KEY (`custody_code`) REFERENCES `custody` (`custody_code`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci



-- إضافة فهارس لتحسين الأداء
CREATE INDEX idx_custody_code ON custody(custody_code);
CREATE INDEX idx_custody_type ON custody(type);
CREATE INDEX idx_delivery_employee ON custody_delivery(employee_code);
CREATE INDEX idx_delivery_custody ON custody_delivery(custody_code);
CREATE INDEX idx_delivery_date ON custody_delivery(delivery_date);



-- إنشاء جدول المساهمات
CREATE TABLE IF NOT EXISTS contributions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_code VARCHAR(50) NOT NULL,
  employee_name VARCHAR(255) NOT NULL,
  contribution_type VARCHAR(50) NOT NULL,
  company_amount DECIMAL(10, 2) NOT NULL,
  fund_amount DECIMAL(10, 2) NOT NULL,
  contribution_date DATE NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إضافة فهرس على كود الموظف
CREATE INDEX idx_employee_code ON contributions(employee_code);

-- إضافة فهرس على نوع المساهمة
CREATE INDEX idx_contribution_type ON contributions(contribution_type);

-- إضافة فهرس على تاريخ المساهمة
CREATE INDEX idx_contribution_date ON contributions(contribution_date);

-- إنشاء جدول المكافآت
CREATE TABLE IF NOT EXISTS rewards (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_code VARCHAR(50) NOT NULL,
  employee_name VARCHAR(255) NOT NULL,
  department VARCHAR(255) NOT NULL,
  amount INT NOT NULL,
  reason VARCHAR(255) NOT NULL,
  reward_date DATE NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إضافة فهرس على كود الموظف في جدول المكافآت
CREATE INDEX idx_rewards_employee_code ON rewards(employee_code);

-- إضافة فهرس على تاريخ المكافأة
CREATE INDEX idx_reward_date ON rewards(reward_date);

-- إنشاء جدول الخصومات
CREATE TABLE IF NOT EXISTS penalties (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_code VARCHAR(50) NOT NULL,
  employee_name VARCHAR(255) NOT NULL,
  department VARCHAR(255) NOT NULL,
  amount INT NOT NULL,
  reason VARCHAR(255) NOT NULL,
  penalty_date DATE NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إضافة فهرس على كود الموظف في جدول الخصومات
CREATE INDEX idx_penalties_employee_code ON penalties(employee_code);

-- إضافة فهرس على تاريخ الخصم
CREATE INDEX idx_penalty_date ON penalties(penalty_date);



-- جدول التقييمات
CREATE TABLE IF NOT EXISTS evaluations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_code VARCHAR(50) NOT NULL,
  employee_name VARCHAR(255) NOT NULL,
  department VARCHAR(255) NOT NULL,
  evaluation_type ENUM('شهري', 'ربع سنوي') NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  score DECIMAL(5,2) NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- فهارس لتحسين الأداء
CREATE INDEX idx_evaluations_employee_code ON evaluations(employee_code);
CREATE INDEX idx_evaluations_type ON evaluations(evaluation_type);
CREATE INDEX idx_evaluations_start_date ON evaluations(start_date);
CREATE INDEX idx_evaluations_end_date ON evaluations(end_date);




-- تعديل جدول المستخدمين لإضافة حقل الصلاحيات
ALTER TABLE `users` ADD COLUMN `permissions` JSON DEFAULT NULL AFTER `password`;

-- إنشاء جدول الصلاحيات الأساسية
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- إدخال الصلاحيات الأساسية
INSERT INTO `permissions` (`name`, `description`) VALUES
('can_view', 'صلاحية عرض البيانات'),
('can_add', 'صلاحية إضافة بيانات جديدة'),
('can_edit', 'صلاحية تعديل البيانات'),
('can_delete', 'صلاحية حذف البيانات'),
('view_employees', 'صلاحية عرض قسم الموظفين'),
('view_vacations', 'صلاحية عرض قسم الإجازات'),
('view_contributions', 'صلاحية عرض قسم المساهمات'),
('view_rewards_penalties', 'صلاحية عرض قسم المكافآت والخصومات'),
('view_custody', 'صلاحية عرض قسم العهد'),
('view_evaluation', 'صلاحية عرض قسم التقييم'),
('view_training', 'صلاحية عرض قسم التدريب'),
('add_training', 'صلاحية إضافة دورة تدريبية جديدة'),
('edit_training', 'صلاحية تعديل الدورات التدريبية'),
('delete_training', 'صلاحية حذف الدورات التدريبية'),
('view_training_reports', 'صلاحية عرض تقارير التدريب'),
('view_resignations', 'صلاحية عرض قسم الاستقالات'),
('add_resignation', 'صلاحية إضافة استقالة جديدة'),
('edit_resignation', 'صلاحية تعديل الاستقالات'),
('delete_resignation', 'صلاحية حذف الاستقالات'),
('view_resignation_reports', 'صلاحية عرض تقارير الاستقالات'),
('view_import', 'صلاحية عرض قسم استيراد البيانات'),
('manage_users', 'صلاحية إدارة المستخدمين والصلاحيات'),
('view_salary_advances', 'صلاحية عرض قسم السلف'),
('add_salary_advance', 'صلاحية إضافة سلفة جديدة'),
('edit_salary_advance', 'صلاحية تعديل السلف'),
('delete_salary_advance', 'صلاحية حذف السلف'),
('view_salary_advance_reports', 'صلاحية عرض تقارير السلف'),
('view_extra_hours', 'صلاحية عرض قسم الإضافي'),
('add_extra_hour', 'صلاحية إضافة عمل إضافي'),
('edit_extra_hour', 'صلاحية تعديل الإضافي'),
('delete_extra_hour', 'صلاحية حذف الإضافي'),
('view_extra_hour_reports', 'صلاحية عرض تقارير الإضافي'),
-- صلاحيات العهد المحددة
('edit_deliver_custody', 'صلاحية تعديل تسليم العهد'),
('delete_deliver_custody', 'صلاحية حذف تسليم العهد'),
('edit_return_custody', 'صلاحية تعديل استرجاع العهد'),
('delete_return_custody', 'صلاحية حذف استرجاع العهد');

-- إنشاء جدول الاستقالات
CREATE TABLE IF NOT EXISTS `resignations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(50) NOT NULL COMMENT 'كود الموظف',
  `employee_name` varchar(255) NOT NULL COMMENT 'اسم الموظف',
  `department` varchar(255) DEFAULT NULL COMMENT 'الإدارة',
  `reason` varchar(255) NOT NULL COMMENT 'سبب الاستقالة',
  `resignation_date` date NOT NULL COMMENT 'تاريخ الاستقالة',
  `work_end_date` date DEFAULT NULL COMMENT 'تاريخ ترك العمل',
  `recommendation` enum('يوصى بعودته','لا يوصى بعودته') NOT NULL COMMENT 'التوصية',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'تاريخ الإنشاء',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'تاريخ التحديث',
  PRIMARY KEY (`id`),
  KEY `idx_employee_code` (`employee_code`),
  KEY `idx_department` (`department`),
  KEY `idx_resignation_date` (`resignation_date`),
  KEY `idx_recommendation` (`recommendation`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الاستقالات';

-- إضافة حقل status إلى جدول الموظفين الموجود
ALTER TABLE `employees` ADD COLUMN `status` ENUM('نشط', 'مستقيل') DEFAULT 'نشط' COMMENT 'حالة الموظف';

-- تحديث جميع الموظفين الحاليين ليكونوا نشطين
UPDATE `employees` SET `status` = 'نشط' WHERE `status` IS NULL;

-- إضافة فهرس على حقل status لتحسين الأداء
CREATE INDEX idx_employee_status ON employees(status);

-- تحديث المستخدم admin ليملك جميع الصلاحيات
UPDATE `users` SET `permissions` = JSON_OBJECT(
  'can_view', true,
  'can_add', true,
  'can_edit', true,
  'can_delete', true,
  'view_employees', true,
  'view_vacations', true,
  'view_contributions', true,
  'view_rewards_penalties', true,
  'view_rewards_list', true,
  'add_reward', true,
  'export_rewards', true,
  'view_deductions_list', true,
  'add_deduction', true,
  'export_deductions', true,
  'view_custody', true,
  'view_evaluation', true,
  'view_training', true,
  'add_training', true,
  'edit_training', true,
  'delete_training', true,
  'view_training_reports', true,
  'view_resignations', true,
  'add_resignation', true,
  'edit_resignation', true,
  'delete_resignation', true,
  'view_resignation_reports', true,
  'view_import', true,
  'manage_users', true,
  'view_salary_advances', true,
  'add_salary_advance', true,
  'edit_salary_advance', true,
  'delete_salary_advance', true,
  'view_salary_advance_reports', true,
  'view_extra_hours', true,
  'add_extra_hour', true,
  'edit_extra_hour', true,
  'delete_extra_hour', true,
  'view_extra_hour_reports', true,
  -- صلاحيات العهد المحددة
  'edit_deliver_custody', true,
  'delete_deliver_custody', true,
  'edit_return_custody', true,
  'delete_return_custody', true
) WHERE `username` = 'admin';

-- ==================== تحديث جدول المستخدمين لربطه بالموظفين ====================

-- إضافة حقل employee_code لجدول المستخدمين الموجود (إذا لم يكن موجوداً)
ALTER TABLE `users`
ADD COLUMN IF NOT EXISTS `employee_code` int DEFAULT NULL COMMENT 'كود الموظف المرتبط بالمستخدم' AFTER `password`;

-- إضافة فهارس ومفاتيح خارجية
ALTER TABLE `users`
ADD UNIQUE KEY IF NOT EXISTS `uk_employee_code` (`employee_code`),
ADD KEY IF NOT EXISTS `idx_employee_code` (`employee_code`);

-- إضافة المفتاح الخارجي (مع التحقق من عدم وجوده مسبقاً)
SET @constraint_exists = (SELECT COUNT(*) FROM information_schema.TABLE_CONSTRAINTS
                         WHERE CONSTRAINT_SCHEMA = DATABASE()
                         AND TABLE_NAME = 'users'
                         AND CONSTRAINT_NAME = 'fk_users_employee');

SET @sql = IF(@constraint_exists = 0,
              'ALTER TABLE `users` ADD CONSTRAINT `fk_users_employee` FOREIGN KEY (`employee_code`) REFERENCES `employees` (`code`) ON DELETE SET NULL ON UPDATE CASCADE',
              'SELECT "Foreign key constraint already exists" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==================== جداول الصور والمستندات ====================

-- إنشاء جدول صور الموظفين
CREATE TABLE IF NOT EXISTS `employee_photos` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(50) NOT NULL COMMENT 'كود الموظف',
  `photo_path` varchar(500) NOT NULL COMMENT 'مسار الصورة',
  `original_name` varchar(255) NOT NULL COMMENT 'الاسم الأصلي للملف',
  `file_size` int NOT NULL COMMENT 'حجم الملف بالبايت',
  `mime_type` varchar(100) NOT NULL COMMENT 'نوع الملف',
  `uploaded_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الرفع',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_employee_photo` (`employee_code`),
  KEY `idx_employee_code` (`employee_code`),
  KEY `idx_uploaded_at` (`uploaded_at`),
  CONSTRAINT `fk_employee_photos_code` FOREIGN KEY (`employee_code`) REFERENCES `employees` (`code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول صور الموظفين';

-- إنشاء جدول مستندات الموظفين
CREATE TABLE IF NOT EXISTS `employee_documents` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(50) NOT NULL COMMENT 'كود الموظف',
  `document_path` varchar(500) NOT NULL COMMENT 'مسار المستند',
  `original_name` varchar(255) NOT NULL COMMENT 'الاسم الأصلي للملف',
  `display_name` varchar(255) NOT NULL COMMENT 'اسم العرض للملف',
  `file_size` int NOT NULL COMMENT 'حجم الملف بالبايت',
  `mime_type` varchar(100) NOT NULL COMMENT 'نوع الملف',
  `file_extension` varchar(10) NOT NULL COMMENT 'امتداد الملف',
  `uploaded_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الرفع',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
  PRIMARY KEY (`id`),
  KEY `idx_employee_code` (`employee_code`),
  KEY `idx_uploaded_at` (`uploaded_at`),
  KEY `idx_file_extension` (`file_extension`),
  CONSTRAINT `fk_employee_documents_code` FOREIGN KEY (`employee_code`) REFERENCES `employees` (`code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول مستندات الموظفين';

-- إضافة فهارس إضافية لتحسين الأداء
CREATE INDEX idx_employee_photos_mime_type ON employee_photos(mime_type);
CREATE INDEX idx_employee_documents_mime_type ON employee_documents(mime_type);
CREATE INDEX idx_employee_documents_display_name ON employee_documents(display_name);

-- ==================== جدول الدورات التدريبية ====================

-- إنشاء جدول الدورات التدريبية
CREATE TABLE IF NOT EXISTS `training_courses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(50) NOT NULL COMMENT 'كود الموظف',
  `employee_name` varchar(255) NOT NULL COMMENT 'اسم الموظف',
  `department` varchar(255) NOT NULL COMMENT 'الإدارة',
  `course_name` varchar(255) NOT NULL COMMENT 'اسم الدورة',
  `course_date` date NOT NULL COMMENT 'تاريخ الدورة',
  `course_duration` int NOT NULL COMMENT 'مدة الدورة بالأيام',
  `training_type` enum('تدريب داخلي','تدريب خارجي','تدريب خاص') NOT NULL COMMENT 'نوع التدريب',
  `course_cost` decimal(10,2) DEFAULT 0.00 COMMENT 'تكلفة الدورة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
  PRIMARY KEY (`id`),
  KEY `idx_employee_code` (`employee_code`),
  KEY `idx_department` (`department`),
  KEY `idx_course_date` (`course_date`),
  KEY `idx_training_type` (`training_type`),
  KEY `idx_course_name` (`course_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الدورات التدريبية';

-- ملاحظة: الفهارس تم إنشاؤها بالفعل في تعريف الجدول أعلاه

-- ==================== جدول السلف ====================

-- إنشاء جدول السلف
CREATE TABLE IF NOT EXISTS `salary_advances` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(50) NOT NULL COMMENT 'كود الموظف',
  `employee_name` varchar(255) NOT NULL COMMENT 'اسم الموظف',
  `department` varchar(255) NOT NULL COMMENT 'الإدارة',
  `advance_amount` decimal(10,2) NOT NULL COMMENT 'قيمة السلفة',
  `advance_date` date NOT NULL COMMENT 'تاريخ السلفة',
  `advance_reason` text NOT NULL COMMENT 'سبب السلفة',
  `payment_method` varchar(255) NOT NULL COMMENT 'طريقة السداد',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات إضافية',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
  PRIMARY KEY (`id`),
  KEY `idx_employee_code` (`employee_code`),
  KEY `idx_department` (`department`),
  KEY `idx_advance_date` (`advance_date`),
  KEY `idx_advance_amount` (`advance_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول السلف';

-- ==================== جدول الساعات الإضافية ====================

-- إنشاء جدول الساعات الإضافية
CREATE TABLE IF NOT EXISTS `extra_hours` (
  `id` int NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(50) NOT NULL COMMENT 'كود الموظف',
  `employee_name` varchar(255) NOT NULL COMMENT 'اسم الموظف',
  `department` varchar(255) NOT NULL COMMENT 'الإدارة',
  `extra_hours` decimal(5,2) NOT NULL COMMENT 'عدد الساعات الإضافية',
  `extra_date` date NOT NULL COMMENT 'تاريخ الإضافي',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
  PRIMARY KEY (`id`),
  KEY `idx_employee_code` (`employee_code`),
  KEY `idx_department` (`department`),
  KEY `idx_extra_date` (`extra_date`),
  KEY `idx_extra_hours` (`extra_hours`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الساعات الإضافية';

-- جدول العامل المثالي
CREATE TABLE IF NOT EXISTS ideal_employees (
  id INT AUTO_INCREMENT PRIMARY KEY,
  employee_code VARCHAR(50) NOT NULL,
  employee_name VARCHAR(255) NOT NULL,
  department VARCHAR(255) NOT NULL,
  from_period DATE NOT NULL,
  to_period DATE NOT NULL,
  evaluation_score DECIMAL(5,2) NOT NULL,
  reward_amount DECIMAL(10,2) NOT NULL,
  selection_reason TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY `idx_employee_code` (`employee_code`),
  KEY `idx_department` (`department`),
  KEY `idx_period` (`from_period`, `to_period`),
  KEY `idx_evaluation_score` (`evaluation_score`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول العامل المثالي';

-- ==================== جدول سجل الأنشطة ====================

-- إنشاء جدول سجل الأنشطة (Activity Log)
CREATE TABLE IF NOT EXISTS `activity_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL COMMENT 'معرف المستخدم',
  `username` varchar(50) NOT NULL COMMENT 'اسم المستخدم',
  `action_type` varchar(50) NOT NULL COMMENT 'نوع العملية (add, edit, delete, login, logout)',
  `module` varchar(50) NOT NULL COMMENT 'القسم (employees, vacations, custody, etc.)',
  `record_id` varchar(50) DEFAULT NULL COMMENT 'معرف السجل المتأثر',
  `message` text NOT NULL COMMENT 'وصف الإجراء',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ التنفيذ',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_module` (`module`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_action_module` (`action_type`, `module`),
  CONSTRAINT `fk_activity_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سجل الأنشطة';