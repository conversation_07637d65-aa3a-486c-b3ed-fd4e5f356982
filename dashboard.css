/* ===== تنسيقات لوحة التحكم الرئيسية ===== */

/* إعدادات عامة للصفحة */
.dashboard-page {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-primary);
    display: flex;
    flex-direction: column;
}

/* ===== رأس الصفحة ===== */
.dashboard-header {
    background: var(--bg-white);
    box-shadow: var(--shadow-medium);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo-section h1 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.user-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}



.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
}

.user-info i {
    font-size: 1.2rem;
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--danger-color);
    color: var(--text-white);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.logout-btn:hover {
    background: var(--danger-hover);
}

/* ===== المحتوى الرئيسي ===== */
.dashboard-main {
    flex: 1;
    padding: 2rem 0;
}

.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ===== قسم الترحيب ===== */
.welcome-section {
    text-align: center;
    margin-bottom: 3rem;
}

.welcome-section h2 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.welcome-section p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0;
}

/* ===== شبكة الكروت ===== */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.2rem;
    margin-bottom: 3rem;
}

/* للشاشات الكبيرة - المزيد من البطاقات */
@media (min-width: 1200px) {
    .cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
    }
}

@media (min-width: 1400px) {
    .cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 1rem;
    }

    .dashboard-card {
        padding: 1.2rem;
    }

    .card-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 0.8rem;
    }

    .card-icon i {
        font-size: 1.3rem;
    }

    .card-title {
        font-size: 1rem;
        margin-bottom: 0.4rem;
    }

    .card-description {
        font-size: 0.8rem;
        line-height: 1.4;
    }
}

/* للشاشات الكبيرة جداً - المزيد من البطاقات */
@media (min-width: 1600px) {
    .cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 0.8rem;
    }

    .dashboard-card {
        padding: 1rem;
    }

    .card-icon {
        width: 45px;
        height: 45px;
        margin-bottom: 0.6rem;
    }

    .card-icon i {
        font-size: 1.2rem;
    }

    .card-title {
        font-size: 0.9rem;
        margin-bottom: 0.3rem;
    }

    .card-description {
        font-size: 0.75rem;
        line-height: 1.3;
    }
}

/* ===== تصميم الكارت ===== */
.dashboard-card {
    background: var(--bg-white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-light);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.dashboard-card:hover::before {
    transform: scaleX(1);
}

/* أيقونة الكارت */
.card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-light), #e8f4fd);
    border-radius: 50%;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.card-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.dashboard-card:hover .card-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    transform: scale(1.1);
}

.dashboard-card:hover .card-icon i {
    color: var(--text-white);
    transform: scale(1.1);
}

/* محتوى الكارت */
.card-content {
    text-align: center;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.2rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.card-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* إجراء الكارت */
.card-action {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover .card-action {
    color: var(--primary-hover);
    transform: translateX(-5px);
}

.card-action i {
    transition: transform 0.3s ease;
}

.dashboard-card:hover .card-action i {
    transform: translateX(-3px);
}

/* ===== تذييل الصفحة ===== */
.dashboard-footer {
    background: var(--bg-white);
    border-top: 1px solid var(--border-light);
    padding: 1.5rem 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.footer-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

/* ===== التصميم المتجاوب ===== */

/* شاشات التابلت */
@media (max-width: 1024px) {
    .cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.2rem;
    }
    
    .dashboard-container {
        padding: 0 1.5rem;
    }
    
    .header-content {
        padding: 0 1.5rem;
    }
    
    .welcome-section h2 {
        font-size: 2rem;
    }
}

/* شاشات الموبايل */
@media (max-width: 768px) {
    .dashboard-main {
        padding: 1.5rem 0;
    }
    
    .dashboard-container {
        padding: 0 1rem;
    }
    
    .header-content {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .user-section {
        flex-direction: column;
        gap: 0.8rem;
        width: 100%;
        text-align: center;
    }


    
    .logo-section h1 {
        font-size: 1.2rem;
    }
    
    .welcome-section {
        margin-bottom: 2rem;
    }
    
    .welcome-section h2 {
        font-size: 1.8rem;
    }
    
    .welcome-section p {
        font-size: 1rem;
    }
    
    .cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
    }

    .dashboard-card {
        padding: 1.2rem;
    }

    .card-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 0.8rem;
    }

    .card-icon i {
        font-size: 1.3rem;
    }

    .card-title {
        font-size: 1rem;
    }

    .card-description {
        font-size: 0.8rem;
    }
}

/* شاشات صغيرة جداً */
@media (max-width: 480px) {
    .dashboard-container {
        padding: 0 0.5rem;
    }
    
    .header-content {
        padding: 0 0.5rem;
    }
    
    .welcome-section h2 {
        font-size: 1.5rem;
    }
    
    .dashboard-card {
        padding: 1rem;
    }

    .card-icon {
        width: 45px;
        height: 45px;
    }

    .card-icon i {
        font-size: 1.2rem;
    }
}

/* ===== حالات خاصة للكروت ===== */

/* كارت مخفي (بدون صلاحية) */
.dashboard-card.hidden {
    display: none;
}

/* تأثير التحميل */
.dashboard-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* تأثير التركيز للوصولية */
.dashboard-card:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسين الأداء - إزالة الانتقالات على الشاشات الصغيرة */
@media (max-width: 768px) {
    .dashboard-card,
    .card-icon,
    .card-icon i,
    .card-action,
    .card-action i {
        transition: none;
    }
}

/* ===== رسالة عدم وجود صلاحيات ===== */
.no-access-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem 2rem;
    background: var(--bg-white);
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-light);
}

.no-access-content i {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.no-access-content h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.no-access-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

/* ===== الإشعارات ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-heavy);
    padding: 1rem 1.5rem;
    z-index: 1000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 400px;
    border-left: 4px solid var(--info-color);
}

.notification.notification-error {
    border-left-color: var(--danger-color);
}

.notification.notification-success {
    border-left-color: var(--success-color);
}

.notification.notification-warning {
    border-left-color: var(--warning-color);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-content i {
    font-size: 1.2rem;
}

.notification-error .notification-content i {
    color: var(--danger-color);
}

.notification-success .notification-content i {
    color: var(--success-color);
}

.notification-warning .notification-content i {
    color: var(--warning-color);
}

.notification-info .notification-content i {
    color: var(--info-color);
}

.notification-content span {
    color: var(--text-primary);
    font-size: 0.95rem;
    line-height: 1.4;
}

/* تنسيقات الإشعارات للموبايل */
@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100%);
    }

    .notification.show {
        transform: translateY(0);
    }
}
